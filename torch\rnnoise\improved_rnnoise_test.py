#!/usr/bin/env python3
"""
改进的RNNoise测试脚本
基于原始RNNoise设计理念，更保守的降噪策略
"""

import numpy as np
import torch
import os
import sys
import struct
import wave
import subprocess
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import rnnoise

def convert_to_mono_48k(input_wav, output_wav):
    """转换音频为单声道48kHz，但保持原始动态"""
    try:
        ffmpeg_path = Path(r"D:\RNNoise\rnnoise-plus-main\env\ffmpeg.exe")
        
        if not ffmpeg_path.exists():
            print(f"❌ FFmpeg不存在: {ffmpeg_path}")
            return False
        
        cmd = [
            str(ffmpeg_path), '-y',
            '-i', input_wav,
            '-ar', '48000',        # 48kHz采样率
            '-ac', '1',            # 单声道
            '-acodec', 'pcm_s16le', # 16位PCM
            output_wav
        ]
        
        print(f"转换音频格式...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print(f"✓ 音频转换成功")
            return True
        else:
            print(f"❌ 音频转换失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 音频转换失败: {e}")
        return False

def extract_rnnoise_features(frame):
    """提取标准RNNoise特征（65维）"""
    try:
        # 确保帧长度为480
        if len(frame) != 480:
            padded_frame = np.zeros(480, dtype=np.float32)
            padded_frame[:min(len(frame), 480)] = frame[:min(len(frame), 480)]
            frame = padded_frame
        
        # FFT变换 (512点)
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame[:256])  # 只取前256个频点
        fft_power = fft_magnitude ** 2
        
        # 初始化65维特征
        features = np.zeros(65, dtype=np.float32)
        
        # 前32维：32个频带的对数功率谱
        for i in range(32):
            start_bin = int(i * 256 / 32)
            end_bin = int((i + 1) * 256 / 32)
            if end_bin > start_bin:
                band_power = np.mean(fft_power[start_bin:end_bin])
                features[i] = np.log10(max(band_power, 1e-10))
        
        # 第33-48维：16个频带的线性功率谱
        for i in range(16):
            start_bin = int(i * 256 / 16)
            end_bin = int((i + 1) * 256 / 16)
            if end_bin > start_bin:
                band_power = np.mean(fft_power[start_bin:end_bin])
                features[32 + i] = band_power
        
        # 第49-65维：时域和频域统计特征
        features[48] = np.log10(max(np.mean(np.abs(frame)), 1e-10))  # 平均幅度
        features[49] = np.log10(max(np.std(frame), 1e-10))           # 标准差
        features[50] = np.log10(max(np.max(np.abs(frame)), 1e-10))   # 峰值
        features[51] = np.log10(max(np.sum(frame ** 2), 1e-10))      # 能量
        
        # 零交叉率
        zero_crossings = np.sum(np.diff(np.sign(frame)) != 0)
        features[52] = zero_crossings / len(frame)
        
        # 频谱质心
        freqs = np.arange(256) * 48000 / 512
        if np.sum(fft_magnitude) > 1e-10:
            spectral_centroid = np.sum(freqs * fft_magnitude) / np.sum(fft_magnitude)
            features[53] = spectral_centroid / 24000  # 归一化到[0,1]
        else:
            features[53] = 0.5
        
        # 频谱带宽
        if np.sum(fft_magnitude) > 1e-10:
            spectral_bandwidth = np.sqrt(np.sum(((freqs - spectral_centroid) ** 2) * fft_magnitude) / np.sum(fft_magnitude))
            features[54] = spectral_bandwidth / 24000
        else:
            features[54] = 0.5
        
        # 频谱滚降点
        cumsum_magnitude = np.cumsum(fft_magnitude)
        if cumsum_magnitude[-1] > 1e-10:
            rolloff_point = np.where(cumsum_magnitude >= 0.85 * cumsum_magnitude[-1])[0]
            if len(rolloff_point) > 0:
                features[55] = rolloff_point[0] / 256
            else:
                features[55] = 1.0
        else:
            features[55] = 0.5
        
        # 其余特征设为小的随机值
        for i in range(56, 65):
            features[i] = np.random.normal(0, 0.001)
        
        return features
        
    except Exception as e:
        print(f"特征提取失败: {e}")
        return np.zeros(65, dtype=np.float32)

def conservative_spectral_denoising(frame, gain, vad_score):
    """保守的频谱降噪，更注重保持语音质量"""
    try:
        if len(frame) == 0:
            return frame
        
        # 确保帧长度
        original_length = len(frame)
        if len(frame) != 480:
            padded_frame = np.zeros(480, dtype=np.float32)
            padded_frame[:min(len(frame), 480)] = frame[:min(len(frame), 480)]
            frame = padded_frame
        
        # FFT变换
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame)
        fft_phase = np.angle(fft_frame)
        
        # 应用32频带增益
        denoised_magnitude = np.copy(fft_magnitude)
        
        # 更保守的VAD阈值
        is_speech = vad_score > 0.5
        
        for band in range(32):
            start_bin = int(band * 256 / 32)
            end_bin = int((band + 1) * 256 / 32)
            
            if end_bin <= start_bin:
                continue
            
            # 获取该频带的增益
            band_gain = gain[band]
            
            # 保守的增益处理
            if is_speech:
                # 语音帧：几乎不降噪，只做轻微处理
                processed_gain = np.clip(band_gain, 0.7, 1.2)
                # 语音保护：确保增益不会太低
                applied_gain = 0.9 + 0.1 * processed_gain
            else:
                # 噪声帧：适度降噪，但不要太激进
                processed_gain = np.clip(band_gain, 0.3, 1.0)
                # 使用平方根而不是平方，降噪更温和
                processed_gain = np.sqrt(processed_gain)
                applied_gain = 0.4 + 0.5 * processed_gain
            
            # 频率相关的微调
            if band < 8:  # 低频 (0-3kHz) - 语音基频区域
                applied_gain = max(applied_gain, 0.8)  # 保护语音基频
            elif band > 24:  # 高频 (12kHz+) - 可以更多降噪
                applied_gain *= 0.9
            
            # 限制增益范围，避免过度处理
            applied_gain = np.clip(applied_gain, 0.3, 1.2)
            
            # 应用增益
            denoised_magnitude[start_bin:end_bin] *= applied_gain
            # 处理对称部分
            if start_bin > 0 and 512-end_bin >= 0 and 512-start_bin <= 512:
                denoised_magnitude[512-end_bin:512-start_bin] *= applied_gain
        
        # 重构复数频谱
        denoised_fft = denoised_magnitude * np.exp(1j * fft_phase)
        
        # IFFT回时域
        denoised_frame = np.real(np.fft.ifft(denoised_fft))[:480]
        
        # 温和的幅度限制
        max_val = max(np.max(np.abs(frame)), 0.1)
        denoised_frame = np.clip(denoised_frame, -max_val, max_val)
        
        return denoised_frame[:original_length]

    except Exception as e:
        print(f"频谱降噪失败: {e}")
        return frame[:original_length] if len(frame) >= original_length else frame

def rnnoise_denoise(model_path, input_wav, output_wav):
    """RNNoise降噪主函数"""
    print(f"🎯 RNNoise保守降噪测试")
    print(f"模型: {model_path}")
    print(f"输入: {input_wav}")
    print(f"输出: {output_wav}")
    
    try:
        # 1. 转换音频格式
        temp_wav = "temp_mono_48k.wav"
        if not convert_to_mono_48k(input_wav, temp_wav):
            return False
        
        # 2. 加载模型
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"使用设备: {device}")
        
        checkpoint = torch.load(model_path, map_location='cpu')
        model_kwargs = checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 256})
        
        model = rnnoise.RNNoise(**model_kwargs)
        model.load_state_dict(checkpoint['state_dict'])
        model.to(device)
        model.eval()
        
        print(f"✓ 模型加载成功 (训练轮次: {checkpoint.get('epoch', 'Unknown')})")
        
        # 3. 读取音频
        with wave.open(temp_wav, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            sample_rate = wav.getframerate()
        
        if len(frames) % 2 != 0:
            frames = frames[:-1]
        
        samples = struct.unpack(f'<{len(frames)//2}h', frames)
        audio_data = np.array(samples, dtype=np.float32) / 32768.0
        
        print(f"音频信息: {len(audio_data)}样本, {len(audio_data)/sample_rate:.2f}秒")
        
        # 4. 逐帧处理
        frame_size = 480
        num_frames = len(audio_data) // frame_size
        denoised_audio = np.copy(audio_data)
        
        print(f"开始降噪处理 ({num_frames}帧)...")
        
        with torch.no_grad():
            states = None
            
            # 分批处理，每次处理200帧
            batch_size = 200
            for i in range(0, num_frames, batch_size):
                end_frame = min(i + batch_size, num_frames)
                
                # 提取特征
                features_batch = []
                for j in range(i, end_frame):
                    start_idx = j * frame_size
                    end_idx = start_idx + frame_size
                    
                    if end_idx <= len(audio_data):
                        frame = audio_data[start_idx:end_idx]
                    else:
                        frame = np.zeros(frame_size)
                        remaining = len(audio_data) - start_idx
                        if remaining > 0:
                            frame[:remaining] = audio_data[start_idx:]
                    
                    # 提取65维特征
                    frame_features = extract_rnnoise_features(frame)
                    features_batch.append(frame_features)
                
                # 转换为tensor并推理
                if features_batch:
                    features_tensor = torch.from_numpy(np.array(features_batch)).unsqueeze(0).to(device)
                    gain, vad, states = model(features_tensor, states)
                    
                    # 应用降噪
                    gain_np = gain.squeeze(0).cpu().numpy()
                    vad_np = vad.squeeze(0).cpu().numpy()
                    
                    for k, (frame_gain, frame_vad) in enumerate(zip(gain_np, vad_np)):
                        frame_idx = i + k
                        if frame_idx < num_frames:
                            start_idx = frame_idx * frame_size
                            end_idx = min(start_idx + frame_size, len(audio_data))
                            
                            if start_idx < len(audio_data):
                                frame = audio_data[start_idx:end_idx]
                                
                                # 应用保守的降噪
                                denoised_frame = conservative_spectral_denoising(
                                    frame, frame_gain, frame_vad[0])
                                
                                denoised_audio[start_idx:end_idx] = denoised_frame
                
                # 显示进度
                if (i // batch_size + 1) % 10 == 0:
                    progress = min(end_frame, num_frames) / num_frames * 100
                    print(f"  进度: {progress:.1f}%")
        
        # 5. 保存结果
        denoised_samples = np.clip(denoised_audio * 32768.0, -32768, 32767).astype(np.int16)
        
        with wave.open(output_wav, 'wb') as wav:
            wav.setnchannels(1)
            wav.setsampwidth(2)
            wav.setframerate(sample_rate)
            wav.writeframes(denoised_samples.tobytes())
        
        # 6. 分析效果
        print(f"\n📊 处理结果:")
        orig_rms = np.sqrt(np.mean(audio_data ** 2))
        denoised_rms = np.sqrt(np.mean(denoised_audio ** 2))
        rms_change = (denoised_rms - orig_rms) / orig_rms * 100
        
        print(f"  RMS变化: {rms_change:+.1f}%")
        
        if abs(rms_change) < 5:
            print("  ✓ 音频能量变化很小，保持了原始动态")
        elif rms_change < -20:
            print("  ⚠️ 音频能量降低较多，可能过度降噪")
        else:
            print("  ✓ 音频处理适中")
        
        print(f"✓ 降噪完成: {output_wav}")
        
        # 清理临时文件
        try:
            os.remove(temp_wav)
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ 降噪失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 RNNoise保守降噪测试")
    print("=" * 50)
    
    current_dir = Path(__file__).parent
    
    # 查找模型文件
    model_path = current_dir / "output" / "checkpoints" / "rnnoise_69.pth"
    if not model_path.exists():
        # 尝试找最新的模型
        checkpoints_dir = current_dir / "output" / "checkpoints"
        if checkpoints_dir.exists():
            checkpoint_files = list(checkpoints_dir.glob("rnnoise_*.pth"))
            if checkpoint_files:
                checkpoint_files.sort(key=lambda x: int(x.stem.split('_')[1]))
                model_path = checkpoint_files[-1]
                print(f"使用最新模型: {model_path.name}")
    
    # 查找音频文件
    audio_file = current_dir / "20250716_084323.wav"
    
    if not model_path.exists():
        print(f"❌ 模型文件不存在")
        return 1
    
    if not audio_file.exists():
        print(f"❌ 音频文件不存在")
        return 1
    
    # 执行降噪
    output_file = current_dir / "conservative_denoised.wav"
    
    if rnnoise_denoise(str(model_path), str(audio_file), str(output_file)):
        print(f"\n🎉 保守降噪完成！")
        print(f"📁 原始音频: {audio_file}")
        print(f"📁 降噪音频: {output_file}")
        print(f"\n🎧 请对比播放两个文件")
        print(f"💡 这次使用了更保守的降噪策略，应该更好地保持语音质量")
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
