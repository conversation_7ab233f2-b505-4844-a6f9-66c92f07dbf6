#!/usr/bin/env python3
"""
RNNoise组件测试脚本
验证所有必要的组件是否正常工作
"""

import os
import sys
import numpy as np
import torch
from pathlib import Path

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        import torch
        print(f"✓ PyTorch: {torch.__version__}")
    except ImportError as e:
        print(f"✗ PyTorch导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy: {np.__version__}")
    except ImportError as e:
        print(f"✗ NumPy导入失败: {e}")
        return False
    
    try:
        # 添加当前目录到Python路径
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))
        
        import rnnoise
        print("✓ RNNoise模块导入成功")
    except ImportError as e:
        print(f"✗ RNNoise模块导入失败: {e}")
        return False
    
    try:
        import wave
        import struct
        import tempfile
        import subprocess
        print("✓ 标准库导入成功")
    except ImportError as e:
        print(f"✗ 标准库导入失败: {e}")
        return False
    
    return True

def test_model_creation():
    """测试模型创建"""
    print("\n=== 测试模型创建 ===")
    
    try:
        import rnnoise
        
        # 创建模型
        model = rnnoise.RNNoise(input_dim=65, output_dim=32, cond_size=128, gru_size=256)
        print("✓ RNNoise模型创建成功")
        
        # 检查参数数量
        param_count = sum(p.numel() for p in model.parameters())
        print(f"✓ 模型参数数量: {param_count}")
        
        # 测试前向传播
        device = torch.device("cpu")
        model.to(device)
        model.eval()
        
        # 创建测试输入
        batch_size = 1
        seq_len = 100
        input_dim = 65
        
        test_input = torch.randn(batch_size, seq_len, input_dim)
        
        with torch.no_grad():
            gain, vad, states = model(test_input)
        
        print(f"✓ 前向传播成功")
        print(f"  输入形状: {test_input.shape}")
        print(f"  增益输出形状: {gain.shape}")
        print(f"  VAD输出形状: {vad.shape}")
        print(f"  状态数量: {len(states)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_paths():
    """测试文件路径"""
    print("\n=== 测试文件路径 ===")
    
    current_dir = Path(__file__).parent
    
    # 检查模型文件
    model_path = current_dir / "output" / "checkpoints" / "rnnoise_69.pth"
    if model_path.exists():
        print(f"✓ 模型文件存在: {model_path}")
        
        # 检查模型文件大小
        size_mb = model_path.stat().st_size / (1024 * 1024)
        print(f"  文件大小: {size_mb:.2f} MB")
    else:
        print(f"✗ 模型文件不存在: {model_path}")
        return False
    
    # 检查音频文件
    audio_path = current_dir / "20250716_084323.wav"
    if audio_path.exists():
        print(f"✓ 音频文件存在: {audio_path}")
        
        # 检查音频文件大小
        size_mb = audio_path.stat().st_size / (1024 * 1024)
        print(f"  文件大小: {size_mb:.2f} MB")
    else:
        print(f"✗ 音频文件不存在: {audio_path}")
        return False
    
    # 检查FFmpeg
    ffmpeg_path = current_dir.parent.parent / "env" / "ffmpeg.exe"
    if ffmpeg_path.exists():
        print(f"✓ FFmpeg存在: {ffmpeg_path}")
    else:
        print(f"⚠️ FFmpeg不存在: {ffmpeg_path}")
        print("  音频转换功能可能不可用")
    
    return True

def test_model_loading():
    """测试模型加载"""
    print("\n=== 测试模型加载 ===")
    
    try:
        current_dir = Path(__file__).parent
        model_path = current_dir / "output" / "checkpoints" / "rnnoise_69.pth"
        
        if not model_path.exists():
            print(f"✗ 模型文件不存在，跳过加载测试")
            return False
        
        # 加载检查点
        checkpoint = torch.load(str(model_path), map_location='cpu')
        print("✓ 检查点加载成功")
        
        # 检查检查点内容
        print(f"  检查点键: {list(checkpoint.keys())}")
        
        if 'state_dict' in checkpoint:
            print("✓ 状态字典存在")
            state_dict = checkpoint['state_dict']
            print(f"  状态字典键数量: {len(state_dict)}")
        
        if 'model_kwargs' in checkpoint:
            model_kwargs = checkpoint['model_kwargs']
            print(f"✓ 模型参数: {model_kwargs}")
        else:
            model_kwargs = {'cond_size': 128, 'gru_size': 256}
            print(f"⚠️ 使用默认模型参数: {model_kwargs}")
        
        if 'epoch' in checkpoint:
            print(f"✓ 训练轮次: {checkpoint['epoch']}")
        
        if 'loss' in checkpoint:
            print(f"✓ 训练损失: {checkpoint['loss']}")
        
        # 创建并加载模型
        import rnnoise
        model = rnnoise.RNNoise(**model_kwargs)
        model.load_state_dict(checkpoint['state_dict'])
        model.eval()
        
        print("✓ 模型加载和初始化成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_processing():
    """测试音频处理"""
    print("\n=== 测试音频处理 ===")
    
    try:
        import wave
        import struct
        
        current_dir = Path(__file__).parent
        audio_path = current_dir / "20250716_084323.wav"
        
        if not audio_path.exists():
            print(f"✗ 音频文件不存在，跳过音频处理测试")
            return False
        
        # 读取WAV文件
        with wave.open(str(audio_path), 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            sample_rate = wav.getframerate()
            channels = wav.getnchannels()
            sampwidth = wav.getsampwidth()
            nframes = wav.getnframes()
        
        print(f"✓ 音频文件读取成功")
        print(f"  采样率: {sample_rate} Hz")
        print(f"  声道数: {channels}")
        print(f"  位深: {sampwidth * 8} 位")
        print(f"  帧数: {nframes}")
        print(f"  时长: {nframes / sample_rate:.2f} 秒")
        
        # 转换为PCM数据
        if len(frames) % 2 != 0:
            frames = frames[:-1]
        
        samples = struct.unpack(f'<{len(frames)//2}h', frames)
        audio_data = np.array(samples, dtype=np.float32) / 32768.0
        
        print(f"✓ PCM数据转换成功")
        print(f"  数据长度: {len(audio_data)} 样本")
        print(f"  数据范围: [{np.min(audio_data):.3f}, {np.max(audio_data):.3f}]")
        
        # 计算帧数
        frame_size = 480
        num_frames = len(audio_data) // frame_size
        print(f"✓ 音频帧分析")
        print(f"  帧大小: {frame_size}")
        print(f"  帧数量: {num_frames}")
        
        return True
        
    except Exception as e:
        print(f"✗ 音频处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 RNNoise组件测试脚本")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("模型创建", test_model_creation),
        ("文件路径", test_file_paths),
        ("模型加载", test_model_loading),
        ("音频处理", test_audio_processing),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("🔍 测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以运行降噪脚本。")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查相关组件。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
