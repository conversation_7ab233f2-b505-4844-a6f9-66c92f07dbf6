#!/usr/bin/env python3
"""
RNNoise降噪测试运行脚本
简化版本，使用预设的路径和参数

使用方法:
python run_denoise_test.py
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    print("🎯 RNNoise降噪测试运行脚本")
    print("=" * 50)
    
    # 获取当前脚本所在目录
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent
    
    # 预设路径
    model_path = current_dir / "output" / "checkpoints" / "rnnoise_69.pth"
    input_wav = current_dir / "20250716_084323.wav"
    output_wav = current_dir / "denoised_20250716_084323.wav"
    temp_dir = current_dir / "temp_denoise"
    
    print(f"项目根目录: {project_root}")
    print(f"当前目录: {current_dir}")
    print(f"模型文件: {model_path}")
    print(f"输入音频: {input_wav}")
    print(f"输出音频: {output_wav}")
    print(f"临时目录: {temp_dir}")
    
    # 检查文件是否存在
    if not model_path.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        print("请确认模型文件路径是否正确")
        return 1
    
    if not input_wav.exists():
        print(f"❌ 输入音频文件不存在: {input_wav}")
        print("请确认音频文件路径是否正确")
        return 1
    
    # 检查Python环境
    try:
        import torch
        import numpy as np
        print(f"✓ PyTorch版本: {torch.__version__}")
        print(f"✓ NumPy版本: {np.__version__}")
        print(f"✓ CUDA可用: {torch.cuda.is_available()}")
    except ImportError as e:
        print(f"❌ Python依赖缺失: {e}")
        return 1
    
    # 检查rnnoise模块
    try:
        sys.path.append(str(current_dir))
        import rnnoise
        print("✓ RNNoise模块加载成功")
    except ImportError as e:
        print(f"❌ RNNoise模块加载失败: {e}")
        print("请确认rnnoise.py文件存在且可导入")
        return 1
    
    # 构建命令
    test_script = current_dir / "test_audio_denoising.py"
    if not test_script.exists():
        print(f"❌ 测试脚本不存在: {test_script}")
        return 1
    
    cmd = [
        sys.executable,  # Python解释器
        str(test_script),
        "--model", str(model_path),
        "--input", str(input_wav),
        "--output", str(output_wav),
        "--temp-dir", str(temp_dir)
    ]
    
    print(f"\n执行命令:")
    print(f"  {' '.join(cmd)}")
    print("\n" + "=" * 50)
    
    # 执行测试脚本
    try:
        result = subprocess.run(cmd, cwd=str(current_dir))
        return result.returncode
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
