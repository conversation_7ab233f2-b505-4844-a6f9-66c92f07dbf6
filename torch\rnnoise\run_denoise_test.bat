@echo off
chcp 65001 >nul
echo 🎯 RNNoise降噪测试运行脚本
echo ==================================================

REM 设置当前目录
cd /d "%~dp0"
echo 当前目录: %CD%

REM 激活Python环境
if exist "..\..\env\Scripts\activate.bat" (
    echo ✓ 激活Python环境...
    call "..\..\env\Scripts\activate.bat"
) else (
    echo ⚠️ 未找到Python环境，使用系统Python
)

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "output\checkpoints\rnnoise_69.pth" (
    echo ❌ 模型文件不存在: output\checkpoints\rnnoise_69.pth
    echo 请确认模型文件路径是否正确
    pause
    exit /b 1
)

if not exist "20250716_084323.wav" (
    echo ❌ 输入音频文件不存在: 20250716_084323.wav
    echo 请确认音频文件路径是否正确
    pause
    exit /b 1
)

if not exist "test_audio_denoising.py" (
    echo ❌ 测试脚本不存在: test_audio_denoising.py
    pause
    exit /b 1
)

if not exist "rnnoise.py" (
    echo ❌ RNNoise模块不存在: rnnoise.py
    pause
    exit /b 1
)

echo ✓ 所有必要文件检查通过

REM 运行降噪测试
echo.
echo 开始运行降噪测试...
echo ==================================================
python run_denoise_test.py

REM 检查结果
if errorlevel 1 (
    echo.
    echo ❌ 降噪测试失败
) else (
    echo.
    echo ✓ 降噪测试完成
    if exist "denoised_20250716_084323.wav" (
        echo ✓ 输出文件已生成: denoised_20250716_084323.wav
        echo.
        echo 🎧 建议:
        echo   1. 播放原始音频: 20250716_084323.wav
        echo   2. 播放降噪音频: denoised_20250716_084323.wav
        echo   3. 对比降噪效果
    )
)

echo.
echo 按任意键退出...
pause >nul
