#!/usr/bin/env python3
"""
调试训练数据和模型匹配问题
分析为什么训练集音频测试没有效果
"""

import numpy as np
import torch
import os
import sys
import struct
import wave
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import rnnoise

def analyze_training_features():
    """分析训练时使用的特征格式"""
    print("🔍 分析训练特征格式")
    print("-" * 50)
    
    # 查找训练特征文件
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent
    
    # 可能的训练数据路径
    possible_paths = [
        project_root / "RNNoise_train" / "training_data",
        project_root / "training_data",
        current_dir / "training_data",
        project_root / "data",
    ]
    
    training_data_found = False
    
    for path in possible_paths:
        if path.exists():
            print(f"✓ 找到可能的训练数据目录: {path}")
            
            # 查找.f32文件
            f32_files = list(path.glob("*.f32"))
            if f32_files:
                print(f"  找到{len(f32_files)}个.f32特征文件")
                
                # 分析第一个文件
                sample_file = f32_files[0]
                try:
                    data = np.memmap(str(sample_file), dtype='float32', mode='r')
                    print(f"  样本文件: {sample_file.name}")
                    print(f"  数据长度: {len(data)}")
                    
                    # 尝试不同的维度
                    for dim in [65, 98, 128]:
                        if len(data) % dim == 0:
                            frames = len(data) // dim
                            print(f"  如果特征维度={dim}: {frames}帧")
                            
                            if frames > 0:
                                reshaped = data[:frames*dim].reshape(frames, dim)
                                print(f"    数据范围: [{np.min(reshaped):.6f}, {np.max(reshaped):.6f}]")
                                print(f"    前5个特征: {reshaped[0, :5]}")
                
                except Exception as e:
                    print(f"  ❌ 分析失败: {e}")
                
                training_data_found = True
                break
    
    if not training_data_found:
        print("⚠️ 未找到训练数据文件")
    
    return training_data_found

def compare_feature_extraction_methods():
    """对比不同的特征提取方法"""
    print("\n🔬 对比特征提取方法")
    print("-" * 50)
    
    # 创建测试音频帧
    sample_rate = 48000
    frame_size = 480
    t = np.linspace(0, frame_size/sample_rate, frame_size)
    
    # 测试不同类型的信号
    test_signals = {
        "纯音": np.sin(2 * np.pi * 440 * t) * 0.5,
        "噪声": np.random.normal(0, 0.1, frame_size),
        "静音": np.zeros(frame_size),
        "混合": np.sin(2 * np.pi * 440 * t) * 0.3 + np.random.normal(0, 0.05, frame_size)
    }
    
    for signal_name, signal in test_signals.items():
        print(f"\n📊 {signal_name}信号特征:")
        
        # 方法1：当前的特征提取
        features_current = extract_features_method1(signal)
        print(f"  方法1 - 范围: [{np.min(features_current):.6f}, {np.max(features_current):.6f}]")
        print(f"  方法1 - 前5维: {features_current[:5]}")
        
        # 方法2：更接近原始RNNoise的特征提取
        features_original = extract_features_method2(signal)
        print(f"  方法2 - 范围: [{np.min(features_original):.6f}, {np.max(features_original):.6f}]")
        print(f"  方法2 - 前5维: {features_original[:5]}")
        
        # 检查是否有异常值
        if np.any(np.isnan(features_current)) or np.any(np.isinf(features_current)):
            print(f"  ⚠️ 方法1有异常值")
        if np.any(np.isnan(features_original)) or np.any(np.isinf(features_original)):
            print(f"  ⚠️ 方法2有异常值")

def extract_features_method1(frame):
    """当前使用的特征提取方法"""
    try:
        # 确保帧长度为480
        if len(frame) != 480:
            padded_frame = np.zeros(480, dtype=np.float32)
            padded_frame[:min(len(frame), 480)] = frame[:min(len(frame), 480)]
            frame = padded_frame
        
        # FFT变换
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame[:256])
        fft_power = fft_magnitude ** 2
        
        features = np.zeros(65, dtype=np.float32)
        
        # 32个频带的对数功率谱
        for i in range(32):
            start_bin = int(i * 256 / 32)
            end_bin = int((i + 1) * 256 / 32)
            if end_bin > start_bin:
                band_power = np.mean(fft_power[start_bin:end_bin])
                features[i] = np.log10(max(band_power, 1e-10))
        
        # 16个频带的线性功率谱
        for i in range(16):
            start_bin = int(i * 256 / 16)
            end_bin = int((i + 1) * 256 / 16)
            if end_bin > start_bin:
                band_power = np.mean(fft_power[start_bin:end_bin])
                features[32 + i] = band_power
        
        # 时域特征
        features[48] = np.log10(max(np.mean(np.abs(frame)), 1e-10))
        features[49] = np.log10(max(np.std(frame), 1e-10))
        features[50] = np.log10(max(np.max(np.abs(frame)), 1e-10))
        features[51] = np.log10(max(np.sum(frame ** 2), 1e-10))
        
        # 零交叉率
        zero_crossings = np.sum(np.diff(np.sign(frame)) != 0)
        features[52] = zero_crossings / len(frame)
        
        # 其他特征
        for i in range(53, 65):
            features[i] = np.random.normal(0, 0.001)
        
        return features
        
    except Exception as e:
        return np.zeros(65, dtype=np.float32)

def extract_features_method2(frame):
    """更接近原始RNNoise的特征提取方法"""
    try:
        # 确保帧长度为480
        if len(frame) != 480:
            padded_frame = np.zeros(480, dtype=np.float32)
            padded_frame[:min(len(frame), 480)] = frame[:min(len(frame), 480)]
            frame = padded_frame
        
        # FFT变换 - 使用窗函数
        windowed_frame = frame * np.hanning(480)
        fft_frame = np.fft.fft(windowed_frame, n=512)
        fft_magnitude = np.abs(fft_frame[:256])
        fft_power = fft_magnitude ** 2
        
        features = np.zeros(65, dtype=np.float32)
        
        # 32个频带 - 使用Bark频率尺度
        bark_bands = np.array([0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 14, 16, 20, 24, 28, 32, 38, 46, 54, 64, 76, 90, 106, 124, 144, 168, 196, 230, 270, 318, 378, 256])
        
        for i in range(32):
            start_bin = bark_bands[i]
            end_bin = bark_bands[i + 1]
            if end_bin > start_bin and end_bin <= 256:
                band_power = np.sum(fft_power[start_bin:end_bin])
                # 使用自然对数而不是log10
                features[i] = np.log(max(band_power, 1e-10))
        
        # 16个频带的线性功率谱 - 归一化
        total_power = np.sum(fft_power) + 1e-10
        for i in range(16):
            start_bin = int(i * 256 / 16)
            end_bin = int((i + 1) * 256 / 16)
            if end_bin > start_bin:
                band_power = np.sum(fft_power[start_bin:end_bin])
                features[32 + i] = band_power / total_power
        
        # 时域特征 - 归一化
        rms = np.sqrt(np.mean(frame ** 2))
        features[48] = np.log(max(rms, 1e-10))
        features[49] = np.log(max(np.std(frame), 1e-10))
        features[50] = np.log(max(np.max(np.abs(frame)), 1e-10))
        
        # 零交叉率
        zero_crossings = np.sum(np.diff(np.sign(frame)) != 0)
        features[51] = zero_crossings / len(frame)
        
        # 频谱质心和带宽
        freqs = np.arange(256) * 48000 / 512
        if np.sum(fft_magnitude) > 1e-10:
            spectral_centroid = np.sum(freqs * fft_magnitude) / np.sum(fft_magnitude)
            features[52] = spectral_centroid / 24000
            
            spectral_bandwidth = np.sqrt(np.sum(((freqs - spectral_centroid) ** 2) * fft_magnitude) / np.sum(fft_magnitude))
            features[53] = spectral_bandwidth / 24000
        else:
            features[52] = 0.5
            features[53] = 0.5
        
        # 其他特征设为0
        for i in range(54, 65):
            features[i] = 0.0
        
        return features
        
    except Exception as e:
        return np.zeros(65, dtype=np.float32)

def test_model_with_different_features():
    """用不同特征测试模型响应"""
    print("\n🧠 测试模型对不同特征的响应")
    print("-" * 50)
    
    try:
        # 加载模型
        current_dir = Path(__file__).parent
        model_path = current_dir / "output" / "checkpoints" / "rnnoise_69.pth"
        
        if not model_path.exists():
            checkpoints_dir = current_dir / "output" / "checkpoints"
            if checkpoints_dir.exists():
                checkpoint_files = list(checkpoints_dir.glob("rnnoise_*.pth"))
                if checkpoint_files:
                    checkpoint_files.sort(key=lambda x: int(x.stem.split('_')[1]))
                    model_path = checkpoint_files[-1]
        
        if not model_path.exists():
            print("❌ 模型文件不存在")
            return False
        
        checkpoint = torch.load(str(model_path), map_location='cpu')
        model_kwargs = checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 256})
        
        model = rnnoise.RNNoise(**model_kwargs)
        model.load_state_dict(checkpoint['state_dict'])
        model.eval()
        
        print(f"✓ 模型加载成功")
        
        # 测试不同的输入特征
        test_cases = [
            ("全零", np.zeros((1, 100, 65))),
            ("全一", np.ones((1, 100, 65))),
            ("小随机", np.random.normal(0, 0.1, (1, 100, 65))),
            ("大随机", np.random.normal(0, 1.0, (1, 100, 65))),
            ("训练范围", np.random.uniform(-10, 10, (1, 100, 65))),
        ]
        
        with torch.no_grad():
            for test_name, test_input in test_cases:
                test_tensor = torch.from_numpy(test_input.astype(np.float32))
                
                try:
                    gain, vad, _ = model(test_tensor)
                    
                    gain_stats = {
                        'min': gain.min().item(),
                        'max': gain.max().item(),
                        'mean': gain.mean().item(),
                        'std': gain.std().item()
                    }
                    
                    vad_stats = {
                        'min': vad.min().item(),
                        'max': vad.max().item(),
                        'mean': vad.mean().item(),
                        'std': vad.std().item()
                    }
                    
                    print(f"📊 {test_name}输入:")
                    print(f"  增益: min={gain_stats['min']:.4f}, max={gain_stats['max']:.4f}, mean={gain_stats['mean']:.4f}, std={gain_stats['std']:.4f}")
                    print(f"  VAD:  min={vad_stats['min']:.4f}, max={vad_stats['max']:.4f}, mean={vad_stats['mean']:.4f}, std={vad_stats['std']:.4f}")
                    
                    # 检查模型是否有响应
                    if gain_stats['std'] < 0.001:
                        print(f"  ⚠️ 增益输出几乎没有变化，模型可能没有学到有效特征")
                    if vad_stats['std'] < 0.001:
                        print(f"  ⚠️ VAD输出几乎没有变化，模型可能没有学到有效特征")
                    
                    # 检查输出范围是否合理
                    if gain_stats['min'] < 0 or gain_stats['max'] > 2:
                        print(f"  ⚠️ 增益输出范围异常")
                    if vad_stats['min'] < 0 or vad_stats['max'] > 1:
                        print(f"  ⚠️ VAD输出范围异常")
                
                except Exception as e:
                    print(f"  ❌ 模型推理失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

def analyze_model_weights():
    """分析模型权重分布"""
    print("\n⚖️ 分析模型权重分布")
    print("-" * 50)
    
    try:
        current_dir = Path(__file__).parent
        model_path = current_dir / "output" / "checkpoints" / "rnnoise_69.pth"
        
        if not model_path.exists():
            checkpoints_dir = current_dir / "output" / "checkpoints"
            if checkpoints_dir.exists():
                checkpoint_files = list(checkpoints_dir.glob("rnnoise_*.pth"))
                if checkpoint_files:
                    checkpoint_files.sort(key=lambda x: int(x.stem.split('_')[1]))
                    model_path = checkpoint_files[-1]
        
        if not model_path.exists():
            print("❌ 模型文件不存在")
            return False
        
        checkpoint = torch.load(str(model_path), map_location='cpu')
        state_dict = checkpoint['state_dict']
        
        print(f"模型权重分析:")
        
        # 分析关键层的权重
        key_layers = ['conv1.weight', 'conv2.weight', 'gru1.weight_ih_l0', 'dense_out.weight']
        
        for layer_name in key_layers:
            if layer_name in state_dict:
                weights = state_dict[layer_name]
                print(f"  {layer_name}:")
                print(f"    形状: {weights.shape}")
                print(f"    范围: [{weights.min().item():.6f}, {weights.max().item():.6f}]")
                print(f"    均值: {weights.mean().item():.6f}")
                print(f"    标准差: {weights.std().item():.6f}")
                
                # 检查权重是否异常
                if torch.any(torch.isnan(weights)):
                    print(f"    ⚠️ 包含NaN值")
                if torch.any(torch.isinf(weights)):
                    print(f"    ⚠️ 包含无穷值")
                if weights.std().item() < 1e-6:
                    print(f"    ⚠️ 权重变化很小，可能训练不充分")
                if weights.std().item() > 10:
                    print(f"    ⚠️ 权重变化很大，可能训练不稳定")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重分析失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 训练数据和模型匹配问题诊断")
    print("=" * 60)
    
    # 执行各项诊断
    diagnostics = [
        ("训练特征分析", analyze_training_features),
        ("特征提取对比", compare_feature_extraction_methods),
        ("模型响应测试", test_model_with_different_features),
        ("模型权重分析", analyze_model_weights),
    ]
    
    results = []
    
    for diag_name, diag_func in diagnostics:
        print(f"\n{'='*20} {diag_name} {'='*20}")
        try:
            result = diag_func()
            results.append((diag_name, result))
        except Exception as e:
            print(f"❌ {diag_name}失败: {e}")
            results.append((diag_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📋 诊断总结")
    print("=" * 60)
    
    for diag_name, result in results:
        status = "✅ 完成" if result else "❌ 失败"
        print(f"{diag_name:20s} : {status}")
    
    print(f"\n💡 可能的问题和解决方案:")
    print(f"  1. 特征提取方法与训练时不匹配")
    print(f"  2. 模型权重可能训练不充分")
    print(f"  3. 输入特征的数值范围与训练时不同")
    print(f"  4. 模型架构与权重文件不匹配")

if __name__ == "__main__":
    main()
