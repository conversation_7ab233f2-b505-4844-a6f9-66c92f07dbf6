#!/usr/bin/env python3
"""
RNNoise降噪问题诊断脚本
系统性排查降噪效果不明显的原因
"""

import numpy as np
import torch
import os
import sys
import struct
import wave
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import rnnoise

def analyze_audio_file(wav_file):
    """分析音频文件的特性"""
    print(f"🔍 分析音频文件: {wav_file}")
    print("-" * 50)
    
    try:
        with wave.open(wav_file, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            sample_rate = wav.getframerate()
            channels = wav.getnchannels()
            sampwidth = wav.getsampwidth()
            nframes = wav.getnframes()
        
        # 转换为数值数据
        if len(frames) % 2 != 0:
            frames = frames[:-1]
        
        samples = struct.unpack(f'<{len(frames)//2}h', frames)
        audio_data = np.array(samples, dtype=np.float32) / 32768.0
        
        # 基本信息
        print(f"📊 基本信息:")
        print(f"  采样率: {sample_rate} Hz")
        print(f"  声道数: {channels}")
        print(f"  位深: {sampwidth * 8} 位")
        print(f"  时长: {nframes / sample_rate:.2f} 秒")
        print(f"  样本数: {len(audio_data)}")
        
        # 音频统计
        print(f"📈 音频统计:")
        print(f"  幅度范围: [{np.min(audio_data):.4f}, {np.max(audio_data):.4f}]")
        print(f"  RMS能量: {np.sqrt(np.mean(audio_data**2)):.4f}")
        print(f"  峰值: {np.max(np.abs(audio_data)):.4f}")
        print(f"  零交叉率: {np.sum(np.diff(np.sign(audio_data)) != 0) / len(audio_data):.4f}")
        
        # 频谱分析
        fft_data = np.fft.fft(audio_data[:min(len(audio_data), 48000)])  # 分析前1秒
        fft_magnitude = np.abs(fft_data[:len(fft_data)//2])
        freqs = np.fft.fftfreq(len(fft_data), 1/sample_rate)[:len(fft_data)//2]
        
        # 频带能量分析
        print(f"🎵 频带能量分析:")
        bands = [(0, 1000), (1000, 4000), (4000, 8000), (8000, 16000), (16000, 24000)]
        for low, high in bands:
            mask = (freqs >= low) & (freqs < high)
            if np.any(mask):
                band_energy = np.mean(fft_magnitude[mask]**2)
                print(f"  {low:5d}-{high:5d}Hz: {band_energy:.6f}")
        
        # 检查是否有明显的噪声特征
        print(f"🔍 噪声特征检查:")
        
        # 高频噪声检查
        high_freq_mask = freqs > 8000
        if np.any(high_freq_mask):
            high_freq_energy = np.mean(fft_magnitude[high_freq_mask]**2)
            total_energy = np.mean(fft_magnitude**2)
            high_freq_ratio = high_freq_energy / (total_energy + 1e-10)
            print(f"  高频能量比例: {high_freq_ratio:.4f}")
            if high_freq_ratio > 0.1:
                print("  ⚠️ 检测到较多高频噪声")
            else:
                print("  ✓ 高频噪声较少")
        
        # 低频噪声检查
        low_freq_mask = freqs < 300
        if np.any(low_freq_mask):
            low_freq_energy = np.mean(fft_magnitude[low_freq_mask]**2)
            total_energy = np.mean(fft_magnitude**2)
            low_freq_ratio = low_freq_energy / (total_energy + 1e-10)
            print(f"  低频能量比例: {low_freq_ratio:.4f}")
            if low_freq_ratio > 0.3:
                print("  ⚠️ 检测到较多低频噪声")
            else:
                print("  ✓ 低频噪声较少")
        
        # 语音活动检测
        frame_size = 480
        num_frames = len(audio_data) // frame_size
        speech_frames = 0
        
        for i in range(num_frames):
            start_idx = i * frame_size
            end_idx = start_idx + frame_size
            frame = audio_data[start_idx:end_idx]
            
            # 简单的VAD：基于能量和零交叉率
            energy = np.sum(frame**2)
            zcr = np.sum(np.diff(np.sign(frame)) != 0) / len(frame)
            
            if energy > 1e-4 and zcr > 0.01:  # 简单的语音检测阈值
                speech_frames += 1
        
        speech_ratio = speech_frames / num_frames if num_frames > 0 else 0
        print(f"  语音帧比例: {speech_ratio:.4f}")
        
        if speech_ratio < 0.3:
            print("  ⚠️ 语音内容较少，可能主要是噪声")
        elif speech_ratio > 0.7:
            print("  ✓ 语音内容丰富")
        else:
            print("  ✓ 语音和噪声混合")
        
        return {
            'sample_rate': sample_rate,
            'duration': nframes / sample_rate,
            'rms_energy': np.sqrt(np.mean(audio_data**2)),
            'peak': np.max(np.abs(audio_data)),
            'speech_ratio': speech_ratio,
            'high_freq_ratio': high_freq_ratio if 'high_freq_ratio' in locals() else 0,
            'low_freq_ratio': low_freq_ratio if 'low_freq_ratio' in locals() else 0
        }
        
    except Exception as e:
        print(f"❌ 音频分析失败: {e}")
        return None

def test_model_inference(model_path, test_input_shape=(1, 100, 65)):
    """测试模型推理行为"""
    print(f"\n🧠 测试模型推理行为")
    print("-" * 50)
    
    try:
        # 加载模型
        checkpoint = torch.load(model_path, map_location='cpu')
        model_kwargs = checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 256})
        
        model = rnnoise.RNNoise(**model_kwargs)
        model.load_state_dict(checkpoint['state_dict'])
        model.eval()
        
        print(f"✓ 模型加载成功")
        
        # 测试不同类型的输入
        test_cases = [
            ("零输入", torch.zeros(test_input_shape)),
            ("随机噪声", torch.randn(test_input_shape) * 0.1),
            ("强噪声", torch.randn(test_input_shape) * 0.5),
            ("正弦波", torch.sin(torch.linspace(0, 10*np.pi, test_input_shape[1]*test_input_shape[2]).reshape(test_input_shape))),
        ]
        
        with torch.no_grad():
            for test_name, test_input in test_cases:
                gain, vad, _ = model(test_input)
                
                gain_stats = {
                    'min': gain.min().item(),
                    'max': gain.max().item(),
                    'mean': gain.mean().item(),
                    'std': gain.std().item()
                }
                
                vad_stats = {
                    'min': vad.min().item(),
                    'max': vad.max().item(),
                    'mean': vad.mean().item()
                }
                
                print(f"📊 {test_name}:")
                print(f"  增益: min={gain_stats['min']:.4f}, max={gain_stats['max']:.4f}, mean={gain_stats['mean']:.4f}, std={gain_stats['std']:.4f}")
                print(f"  VAD:  min={vad_stats['min']:.4f}, max={vad_stats['max']:.4f}, mean={vad_stats['mean']:.4f}")
                
                # 检查输出是否合理
                if gain_stats['min'] == gain_stats['max']:
                    print("  ⚠️ 增益输出完全相同，可能模型有问题")
                elif gain_stats['std'] < 0.01:
                    print("  ⚠️ 增益变化很小，降噪效果可能不明显")
                else:
                    print("  ✓ 增益输出有合理变化")
                
                if vad_stats['min'] == vad_stats['max']:
                    print("  ⚠️ VAD输出完全相同，可能模型有问题")
                else:
                    print("  ✓ VAD输出有变化")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型推理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_before_after_audio(original_file, denoised_file):
    """对比降噪前后的音频"""
    print(f"\n🔄 对比降噪前后音频")
    print("-" * 50)
    
    try:
        # 分析原始音频
        print("📥 原始音频:")
        orig_stats = analyze_audio_file(original_file)
        
        print("\n📤 降噪音频:")
        denoised_stats = analyze_audio_file(denoised_file)
        
        if orig_stats and denoised_stats:
            print(f"\n📊 对比结果:")
            
            # 能量变化
            energy_change = (denoised_stats['rms_energy'] - orig_stats['rms_energy']) / orig_stats['rms_energy']
            print(f"  RMS能量变化: {energy_change*100:+.1f}%")
            
            # 峰值变化
            peak_change = (denoised_stats['peak'] - orig_stats['peak']) / orig_stats['peak']
            print(f"  峰值变化: {peak_change*100:+.1f}%")
            
            # 语音比例变化
            speech_change = denoised_stats['speech_ratio'] - orig_stats['speech_ratio']
            print(f"  语音比例变化: {speech_change:+.3f}")
            
            # 评估降噪效果
            print(f"\n🎯 降噪效果评估:")
            
            if abs(energy_change) < 0.05:
                print("  ⚠️ 能量变化很小，降噪效果可能不明显")
            elif energy_change < -0.1:
                print("  ✓ 能量明显降低，可能有降噪效果")
            elif energy_change > 0.1:
                print("  ⚠️ 能量增加，可能有问题")
            
            if abs(speech_change) > 0.2:
                print("  ⚠️ 语音比例变化较大，可能影响语音质量")
            else:
                print("  ✓ 语音比例变化合理")
        
        return True
        
    except Exception as e:
        print(f"❌ 音频对比失败: {e}")
        return False

def diagnose_feature_extraction(wav_file):
    """诊断特征提取过程"""
    print(f"\n🔧 诊断特征提取过程")
    print("-" * 50)
    
    try:
        # 读取音频
        with wave.open(wav_file, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
        
        if len(frames) % 2 != 0:
            frames = frames[:-1]
        
        samples = struct.unpack(f'<{len(frames)//2}h', frames)
        audio_data = np.array(samples, dtype=np.float32) / 32768.0
        
        frame_size = 480
        num_frames = min(10, len(audio_data) // frame_size)  # 只分析前10帧
        
        print(f"📊 分析前{num_frames}帧的特征:")
        
        for i in range(num_frames):
            start_idx = i * frame_size
            end_idx = start_idx + frame_size
            frame = audio_data[start_idx:end_idx]
            
            # FFT分析
            fft_frame = np.fft.fft(frame, n=512)
            fft_magnitude = np.abs(fft_frame[:256])
            fft_power = fft_magnitude ** 2
            
            # 计算32个频带的能量
            band_energies = []
            for band in range(32):
                start_bin = int(band * 256 / 32)
                end_bin = int((band + 1) * 256 / 32)
                band_energy = np.mean(fft_power[start_bin:end_bin])
                band_energies.append(band_energy)
            
            band_energies = np.array(band_energies)
            
            print(f"  帧{i+1:2d}: 能量={np.sum(frame**2):.6f}, 频带能量范围=[{np.min(band_energies):.6f}, {np.max(band_energies):.6f}]")
            
            # 检查是否有异常
            if np.sum(frame**2) < 1e-8:
                print(f"    ⚠️ 帧{i+1}能量极低，可能是静音")
            elif np.all(band_energies < 1e-8):
                print(f"    ⚠️ 帧{i+1}所有频带能量极低")
            elif np.std(band_energies) < 1e-8:
                print(f"    ⚠️ 帧{i+1}频带能量变化极小")
            else:
                print(f"    ✓ 帧{i+1}特征正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 特征提取诊断失败: {e}")
        return False

def main():
    """主诊断函数"""
    print("🔧 RNNoise降噪问题诊断工具")
    print("=" * 60)
    
    # 检查文件路径
    current_dir = Path(__file__).parent
    model_path = current_dir / "output" / "checkpoints" / "rnnoise_69.pth"
    audio_file = current_dir / "20250716_084323.wav"
    
    # 如果rnnoise_69.pth不存在，尝试使用最新的
    if not model_path.exists():
        checkpoints_dir = current_dir / "output" / "checkpoints"
        if checkpoints_dir.exists():
            checkpoint_files = list(checkpoints_dir.glob("rnnoise_*.pth"))
            if checkpoint_files:
                # 按数字排序，取最大的
                checkpoint_files.sort(key=lambda x: int(x.stem.split('_')[1]))
                model_path = checkpoint_files[-1]
                print(f"📍 使用最新模型: {model_path}")
    
    print(f"📍 模型文件: {model_path}")
    print(f"📍 音频文件: {audio_file}")
    
    # 执行诊断
    diagnostics = [
        ("音频文件分析", lambda: analyze_audio_file(str(audio_file)) if audio_file.exists() else print("❌ 音频文件不存在")),
        ("模型推理测试", lambda: test_model_inference(str(model_path)) if model_path.exists() else print("❌ 模型文件不存在")),
        ("特征提取诊断", lambda: diagnose_feature_extraction(str(audio_file)) if audio_file.exists() else print("❌ 音频文件不存在")),
    ]
    
    results = []
    
    for diag_name, diag_func in diagnostics:
        print(f"\n{'='*20} {diag_name} {'='*20}")
        try:
            result = diag_func()
            results.append((diag_name, result))
        except Exception as e:
            print(f"❌ {diag_name}失败: {e}")
            results.append((diag_name, False))
    
    # 检查是否有降噪后的文件进行对比
    denoised_files = list(current_dir.glob("denoised_*.wav"))
    if denoised_files and audio_file.exists():
        print(f"\n{'='*20} 降噪效果对比 {'='*20}")
        compare_before_after_audio(str(audio_file), str(denoised_files[0]))
    
    # 总结
    print(f"\n{'='*60}")
    print("📋 诊断总结")
    print("=" * 60)
    
    for diag_name, result in results:
        status = "✅ 完成" if result else "❌ 失败"
        print(f"{diag_name:20s} : {status}")
    
    print(f"\n💡 建议:")
    print(f"  1. 检查音频文件是否包含明显的噪声")
    print(f"  2. 确认模型权重是否正确加载")
    print(f"  3. 验证特征提取是否正常")
    print(f"  4. 对比降噪前后的音频文件")

if __name__ == "__main__":
    main()
