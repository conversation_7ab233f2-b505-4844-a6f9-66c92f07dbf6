#!/usr/bin/env python3
"""
RNNoise音频降噪验证脚本
基于torch/rnnoise训练架构的完整验证流程
使用指定的模型对音频进行降噪处理并生成对比结果

使用方法:
python test_audio_denoising.py --model output/checkpoints/rnnoise_69.pth --input 20250716_084323.wav --output denoised_output.wav
"""

import numpy as np
import torch
from torch import nn
import torch.nn.functional as F
import os
import sys
import struct
import wave
import argparse
import tempfile
import subprocess
import shutil
from pathlib import Path

# 添加当前目录到Python路径，以便导入rnnoise模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import rnnoise

def setup_ffmpeg_path():
    """设置FFmpeg路径"""
    # 获取项目根目录
    project_root = Path(__file__).parent.parent.parent
    ffmpeg_dir = project_root / "env"
    
    # 添加FFmpeg到环境变量
    if ffmpeg_dir.exists():
        env_path = os.environ.get('PATH', '')
        ffmpeg_path = str(ffmpeg_dir)
        if ffmpeg_path not in env_path:
            os.environ['PATH'] = f"{ffmpeg_path};{env_path}"
        print(f"✓ FFmpeg路径已设置: {ffmpeg_path}")
        return True
    else:
        print(f"⚠️ FFmpeg目录不存在: {ffmpeg_dir}")
        return False

def convert_audio_to_48k(input_wav, output_wav):
    """使用FFmpeg将音频转换为48kHz采样率"""
    try:
        cmd = [
            'ffmpeg', '-y',  # -y 覆盖输出文件
            '-i', input_wav,
            '-ar', '48000',  # 设置采样率为48kHz
            '-ac', '1',      # 单声道
            '-acodec', 'pcm_s16le',  # 16位PCM编码
            output_wav
        ]
        
        print(f"执行音频转换: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print(f"✓ 音频转换成功: {output_wav}")
            return True
        else:
            print(f"✗ 音频转换失败:")
            print(f"  stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 音频转换超时")
        return False
    except Exception as e:
        print(f"✗ 音频转换失败: {e}")
        return False

def wav_to_pcm(wav_file, pcm_file):
    """将WAV文件转换为PCM文件"""
    try:
        with wave.open(wav_file, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            
        with open(pcm_file, 'wb') as f:
            f.write(frames)
        
        print(f"✓ WAV转PCM成功: {pcm_file}")
        return True
    except Exception as e:
        print(f"✗ WAV转PCM失败: {e}")
        return False

def pcm_to_wav(pcm_file, wav_file, reference_wav=None, sample_rate=48000):
    """将PCM文件转换为WAV文件"""
    try:
        with open(pcm_file, 'rb') as f:
            pcm_data = f.read()
        
        # 获取参考WAV文件的参数
        if reference_wav and os.path.exists(reference_wav):
            with wave.open(reference_wav, 'rb') as ref_wav:
                channels = ref_wav.getnchannels()
                sampwidth = ref_wav.getsampwidth()
                framerate = ref_wav.getframerate()
        else:
            channels = 1
            sampwidth = 2
            framerate = sample_rate
        
        with wave.open(wav_file, 'wb') as wav:
            wav.setnchannels(channels)
            wav.setsampwidth(sampwidth)
            wav.setframerate(framerate)
            wav.writeframes(pcm_data)
        
        print(f"✓ PCM转WAV成功: {wav_file}")
        return True
    except Exception as e:
        print(f"✗ PCM转WAV失败: {e}")
        return False

def extract_features_from_audio(wav_file):
    """从WAV音频文件提取RNNoise特征"""
    try:
        print(f"开始提取特征: {wav_file}")
        
        # 读取WAV文件
        with wave.open(wav_file, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            sample_rate = wav.getframerate()
            channels = wav.getnchannels()
            sampwidth = wav.getsampwidth()
        
        print(f"  音频信息: {sample_rate}Hz, {channels}声道, {sampwidth*8}位")
        
        # 转换为PCM数据
        if len(frames) % 2 != 0:
            frames = frames[:-1]
        
        samples = struct.unpack(f'<{len(frames)//2}h', frames)
        audio_data = np.array(samples, dtype=np.float32) / 32768.0
        
        frame_size = 480  # RNNoise标准帧大小
        num_frames = len(audio_data) // frame_size
        
        if num_frames == 0:
            print("✗ 音频太短，无法提取特征")
            return None
        
        print(f"  音频长度: {len(audio_data)} 样本, {num_frames} 帧")
        
        # 提取98维特征
        features = []
        for i in range(num_frames):
            start_idx = i * frame_size
            end_idx = start_idx + frame_size
            
            if end_idx > len(audio_data):
                frame = np.zeros(frame_size, dtype=np.float32)
                frame[:len(audio_data) - start_idx] = audio_data[start_idx:]
            else:
                frame = audio_data[start_idx:end_idx]
            
            # 生成98维特征 (65维输入 + 32维增益目标 + 1维VAD目标)
            frame_features = extract_98d_features(frame)
            features.append(frame_features)
        
        features_array = np.array(features, dtype=np.float32)
        
        # 保存到临时文件
        temp_features = tempfile.NamedTemporaryFile(suffix='.f32', delete=False)
        temp_features_path = temp_features.name
        temp_features.close()
        
        features_array.flatten().tofile(temp_features_path)
        
        print(f"✓ 特征提取成功: {features_array.shape} -> {temp_features_path}")
        return temp_features_path
        
    except Exception as e:
        print(f"✗ 特征提取失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def extract_98d_features(frame):
    """提取单帧的98维特征"""
    try:
        # FFT变换
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame[:256])
        fft_power = fft_magnitude ** 2
        
        # 输入特征 (65维)
        input_features = np.zeros(65, dtype=np.float32)
        
        # 频谱能量特征 (32维)
        for i in range(32):
            start_bin = int(i * 256 / 32)
            end_bin = int((i + 1) * 256 / 32)
            input_features[i] = np.mean(fft_magnitude[start_bin:end_bin])
        
        # 功率谱特征 (16维)
        for i in range(16):
            start_bin = int(i * 256 / 16)
            end_bin = int((i + 1) * 256 / 16)
            input_features[32 + i] = np.mean(fft_power[start_bin:end_bin])
        
        # 时域特征 (17维)
        input_features[48] = np.mean(np.abs(frame))  # 平均幅度
        input_features[49] = np.std(frame)           # 标准差
        input_features[50] = np.max(np.abs(frame))   # 峰值
        input_features[51] = np.sum(frame ** 2)      # 能量
        
        # 零交叉率
        zero_crossings = np.sum(np.diff(np.sign(frame)) != 0)
        input_features[52] = zero_crossings / len(frame)
        
        # 频谱质心
        freqs = np.arange(256) * 48000 / 512
        spectral_centroid = np.sum(freqs * fft_magnitude) / (np.sum(fft_magnitude) + 1e-10)
        input_features[53] = spectral_centroid / 24000  # 归一化
        
        # 其他特征填充
        for i in range(54, 65):
            input_features[i] = np.random.normal(0, 0.1)  # 随机噪声特征
        
        # 目标增益 (32维) - 初始化为1.0
        target_gain = np.ones(32, dtype=np.float32)
        
        # 目标VAD (1维) - 初始化为1.0 (假设是语音)
        target_vad = np.array([1.0], dtype=np.float32)
        
        # 合并为98维特征
        features_98d = np.concatenate([input_features, target_gain, target_vad])
        
        return features_98d
        
    except Exception as e:
        print(f"单帧特征提取失败: {e}")
        return np.zeros(98, dtype=np.float32)

def load_model(model_path, device):
    """加载训练好的RNNoise模型"""
    print(f"加载模型: {model_path}")
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    # 加载检查点
    checkpoint = torch.load(model_path, map_location='cpu')
    
    # 创建模型 - 使用与训练时相同的参数
    model_args = checkpoint.get('model_args', ())
    model_kwargs = checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 256})
    
    print(f"模型参数: args={model_args}, kwargs={model_kwargs}")
    
    model = rnnoise.RNNoise(*model_args, **model_kwargs)
    model.load_state_dict(checkpoint['state_dict'])
    model.to(device)
    model.eval()
    
    print(f"✓ 模型加载成功")
    print(f"  参数数量: {sum(p.numel() for p in model.parameters())}")
    print(f"  训练轮次: {checkpoint.get('epoch', 'Unknown')}")
    print(f"  训练损失: {checkpoint.get('loss', 'Unknown')}")
    
    return model

class RNNoiseTestDataset(torch.utils.data.Dataset):
    """RNNoise测试数据集类"""
    def __init__(self, features_file, sequence_length=2000):
        self.sequence_length = sequence_length

        # 加载特征数据
        self.data = np.memmap(features_file, dtype='float32', mode='r')
        dim = 98

        self.nb_sequences = self.data.shape[0] // self.sequence_length // dim
        if self.nb_sequences == 0:
            # 如果数据不足一个完整序列，创建一个较短的序列
            actual_length = self.data.shape[0] // dim
            if actual_length > 0:
                self.sequence_length = actual_length
                self.nb_sequences = 1
                self.data = self.data[:actual_length * dim]
                self.data = np.reshape(self.data, (1, actual_length, dim))
            else:
                raise ValueError("特征数据不足")
        else:
            self.data = self.data[:self.nb_sequences * self.sequence_length * dim]
            self.data = np.reshape(self.data, (self.nb_sequences, self.sequence_length, dim))

        print(f"测试数据集:")
        print(f"  序列数量: {self.nb_sequences}")
        print(f"  序列长度: {self.sequence_length}")
        print(f"  特征维度: {dim}")

    def __len__(self):
        return self.nb_sequences

    def __getitem__(self, index):
        return (self.data[index, :, :65].copy(),      # 输入特征 (65维)
                self.data[index, :, 65:-1].copy(),    # 目标增益 (32维)
                self.data[index, :, -1:].copy())      # 目标VAD (1维)

def denoise_with_rnnoise(model, features_file, pcm_file, output_pcm_file, device, gamma=0.25):
    """使用RNNoise模型进行降噪处理"""
    try:
        print("开始RNNoise降噪处理...")

        # 1. 加载测试数据集
        test_dataset = RNNoiseTestDataset(features_file)

        # 2. 读取原始音频数据
        with open(pcm_file, 'rb') as f:
            data = f.read()

        if len(data) % 2 != 0:
            data = data[:-1]

        samples = struct.unpack(f'<{len(data)//2}h', data)
        audio_data = np.array(samples, dtype=np.float32) / 32768.0

        print(f"原始音频长度: {len(audio_data)} 样本")

        # 3. 使用模型进行推理
        denoised_audio = np.copy(audio_data)
        frame_size = 480  # RNNoise标准帧大小

        with torch.no_grad():
            states = None  # 初始化RNN状态

            # 处理每个序列
            for seq_idx in range(len(test_dataset)):
                features, _, _ = test_dataset[seq_idx]

                # 转换为tensor
                features_tensor = torch.from_numpy(features).unsqueeze(0).to(device)  # [1, seq_len, 65]

                # RNNoise前向传播
                pred_gain, pred_vad, states = model(features_tensor, states)

                # 获取预测结果
                pred_gain = pred_gain.squeeze(0).cpu().numpy()  # [seq_len, 32]
                pred_vad = pred_vad.squeeze(0).cpu().numpy()    # [seq_len, 1]

                # 应用降噪到对应的音频段
                start_audio_idx = seq_idx * test_dataset.sequence_length * frame_size

                # 逐帧应用降噪
                for frame_idx in range(len(pred_gain)):
                    audio_start = start_audio_idx + frame_idx * frame_size
                    audio_end = min(audio_start + frame_size, len(audio_data))

                    if audio_start < len(audio_data) and audio_end <= len(audio_data):
                        frame = audio_data[audio_start:audio_end]
                        gain = pred_gain[frame_idx]
                        vad_score = pred_vad[frame_idx][0]

                        # 应用频域降噪
                        denoised_frame = apply_spectral_denoising(frame, gain, vad_score, gamma)

                        # 确保帧长度匹配
                        if len(denoised_frame) == len(frame):
                            denoised_audio[audio_start:audio_end] = denoised_frame

        # 4. 保存降噪后的音频
        denoised_samples = np.clip(denoised_audio * 32768.0, -32768, 32767).astype(np.int16)

        with open(output_pcm_file, 'wb') as f:
            f.write(denoised_samples.tobytes())

        print(f"✓ 降噪处理完成: {output_pcm_file}")
        print(f"✓ 输出长度: {len(denoised_samples)} 样本")
        return True

    except Exception as e:
        print(f"✗ 降噪处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def apply_spectral_denoising(frame, gain, vad_score, gamma=0.25):
    """应用频谱降噪"""
    try:
        if len(frame) == 0:
            return frame

        # 确保帧长度
        frame_size = 480
        original_length = len(frame)

        if len(frame) < frame_size:
            padded_frame = np.zeros(frame_size, dtype=np.float32)
            padded_frame[:len(frame)] = frame
            frame = padded_frame
        elif len(frame) > frame_size:
            frame = frame[:frame_size]

        # FFT变换
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame)
        fft_phase = np.angle(fft_frame)

        # 应用32频带增益
        denoised_magnitude = np.copy(fft_magnitude)

        for band in range(32):
            # 计算每个频带对应的FFT bin范围
            start_bin = int(band * 256 / 32)
            end_bin = int((band + 1) * 256 / 32)

            # 获取该频带的增益
            band_gain = gain[band]

            # 处理增益
            processed_gain = max(0, band_gain)
            processed_gain = processed_gain * (np.tanh(8 * processed_gain) ** 2)
            processed_gain = processed_gain ** gamma

            # 根据VAD调整增益
            if vad_score > 0.5:  # 语音帧
                applied_gain = 0.8 + 0.2 * processed_gain
            else:  # 噪声帧
                applied_gain = processed_gain

            # 限制增益范围
            applied_gain = np.clip(applied_gain, 0.1, 1.5)

            # 应用增益
            denoised_magnitude[start_bin:end_bin] *= applied_gain
            # 处理对称部分
            if start_bin > 0:
                denoised_magnitude[512-end_bin:512-start_bin] *= applied_gain

        # 重构复数频谱
        denoised_fft = denoised_magnitude * np.exp(1j * fft_phase)

        # IFFT回时域
        denoised_frame = np.real(np.fft.ifft(denoised_fft))[:frame_size]

        # 限制幅度
        denoised_frame = np.clip(denoised_frame, -1.0, 1.0)

        return denoised_frame[:original_length]

    except Exception as e:
        print(f"频谱降噪失败: {e}")
        return frame

def test_single_audio(model_path, input_wav, output_wav, temp_dir="temp_denoise"):
    """测试单个音频文件的降噪效果"""
    print("=== RNNoise音频降噪验证 ===")
    print(f"模型路径: {model_path}")
    print(f"输入音频: {input_wav}")
    print(f"输出音频: {output_wav}")

    # 创建临时目录
    os.makedirs(temp_dir, exist_ok=True)

    # 设置FFmpeg路径
    setup_ffmpeg_path()

    # 设备选择
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    try:
        # 1. 验证输入文件
        if not os.path.exists(input_wav):
            print(f"✗ 输入音频文件不存在: {input_wav}")
            return False

        if not os.path.exists(model_path):
            print(f"✗ 模型文件不存在: {model_path}")
            return False

        # 2. 转换音频为48kHz (如果需要)
        temp_48k_wav = os.path.join(temp_dir, "temp_48k.wav")
        if not convert_audio_to_48k(input_wav, temp_48k_wav):
            print("✗ 音频格式转换失败")
            return False

        # 3. 加载模型
        model = load_model(model_path, device)

        # 4. WAV转PCM
        temp_pcm = os.path.join(temp_dir, "temp_input.pcm")
        if not wav_to_pcm(temp_48k_wav, temp_pcm):
            print("✗ WAV转PCM失败")
            return False

        # 5. 提取特征
        features_file = extract_features_from_audio(temp_48k_wav)
        if features_file is None:
            print("✗ 特征提取失败")
            return False

        # 6. RNNoise降噪
        denoised_pcm = os.path.join(temp_dir, "temp_denoised.pcm")
        if not denoise_with_rnnoise(model, features_file, temp_pcm, denoised_pcm, device):
            print("✗ 降噪处理失败")
            return False

        # 7. PCM转WAV (降噪后)
        if not pcm_to_wav(denoised_pcm, output_wav, reference_wav=temp_48k_wav):
            print("✗ 降噪音频PCM转WAV失败")
            return False

        # 8. 验证输出文件
        try:
            with wave.open(temp_48k_wav, 'rb') as orig:
                orig_frames = orig.getnframes()
                orig_rate = orig.getframerate()
                orig_channels = orig.getnchannels()

            with wave.open(output_wav, 'rb') as denoised:
                denoised_frames = denoised.getnframes()
                denoised_rate = denoised.getframerate()
                denoised_channels = denoised.getnchannels()

            print(f"文件验证:")
            print(f"  原始: {orig_frames}帧, {orig_rate}Hz, {orig_channels}声道")
            print(f"  降噪: {denoised_frames}帧, {denoised_rate}Hz, {denoised_channels}声道")

            # 计算音频时长
            orig_duration = orig_frames / orig_rate
            denoised_duration = denoised_frames / denoised_rate
            print(f"  原始时长: {orig_duration:.2f}秒")
            print(f"  降噪时长: {denoised_duration:.2f}秒")

        except Exception as e:
            print(f"⚠️ 文件验证失败: {e}")

        # 9. 清理临时文件
        try:
            if os.path.exists(features_file):
                os.unlink(features_file)
            # 保留temp_dir中的文件用于调试
        except:
            pass

        print(f"✓ 降噪处理完成!")
        print(f"✓ 输出文件: {output_wav}")
        print(f"\n🎧 建议:")
        print(f"  1. 播放原始音频: {input_wav}")
        print(f"  2. 播放降噪音频: {output_wav}")
        print(f"  3. 对比降噪效果")

        return True

    except Exception as e:
        print(f"✗ 处理过程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='RNNoise音频降噪验证脚本')
    parser.add_argument('--model', '-m', required=True,
                       help='RNNoise模型文件路径 (例如: output/checkpoints/rnnoise_69.pth)')
    parser.add_argument('--input', '-i', required=True,
                       help='输入音频文件路径 (例如: 20250716_084323.wav)')
    parser.add_argument('--output', '-o', required=True,
                       help='输出降噪音频文件路径 (例如: denoised_output.wav)')
    parser.add_argument('--temp-dir', '-t', default='temp_denoise',
                       help='临时文件目录 (默认: temp_denoise)')

    args = parser.parse_args()

    print("🎯 RNNoise音频降噪验证脚本")
    print("=" * 50)

    # 转换为绝对路径
    model_path = os.path.abspath(args.model)
    input_wav = os.path.abspath(args.input)
    output_wav = os.path.abspath(args.output)
    temp_dir = os.path.abspath(args.temp_dir)

    # 开始处理
    try:
        success = test_single_audio(model_path, input_wav, output_wav, temp_dir)

        if success:
            print("\n🎉 降噪验证成功完成！")
            print(f"📁 输出文件: {output_wav}")
            print(f"📁 临时文件: {temp_dir}")
        else:
            print("\n❌ 降噪验证失败")
            return 1

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 验证过程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
