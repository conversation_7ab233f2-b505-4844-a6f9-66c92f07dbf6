#!/usr/bin/env python3
"""
改进版RNNoise音频降噪验证脚本
增强降噪效果，添加调试信息和参数调优

使用方法:
python test_audio_denoising_improved.py --model output/checkpoints/rnnoise_69.pth --input 20250716_084323.wav --output denoised_output.wav
"""

import numpy as np
import torch
from torch import nn
import torch.nn.functional as F
import os
import sys
import struct
import wave
import argparse
import tempfile
import subprocess
import shutil
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import rnnoise

def convert_audio_to_48k(input_wav, output_wav):
    """使用FFmpeg将音频转换为48kHz采样率"""
    try:
        ffmpeg_path = Path(r"D:\RNNoise\rnnoise-plus-main\env\ffmpeg.exe")
        
        if not ffmpeg_path.exists():
            print(f"✗ FFmpeg不存在: {ffmpeg_path}")
            return False
        
        cmd = [
            str(ffmpeg_path), '-y',
            '-i', input_wav,
            '-ar', '48000',
            '-ac', '1',
            '-acodec', 'pcm_s16le',
            output_wav
        ]
        
        print(f"执行音频转换: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print(f"✓ 音频转换成功: {output_wav}")
            return True
        else:
            print(f"✗ 音频转换失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 音频转换失败: {e}")
        return False

def wav_to_pcm(wav_file, pcm_file):
    """将WAV文件转换为PCM文件"""
    try:
        with wave.open(wav_file, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
        with open(pcm_file, 'wb') as f:
            f.write(frames)
        return True
    except Exception as e:
        print(f"✗ WAV转PCM失败: {e}")
        return False

def pcm_to_wav(pcm_file, wav_file, reference_wav=None, sample_rate=48000):
    """将PCM文件转换为WAV文件"""
    try:
        with open(pcm_file, 'rb') as f:
            pcm_data = f.read()
        
        if reference_wav and os.path.exists(reference_wav):
            with wave.open(reference_wav, 'rb') as ref_wav:
                channels = ref_wav.getnchannels()
                sampwidth = ref_wav.getsampwidth()
                framerate = ref_wav.getframerate()
        else:
            channels = 1
            sampwidth = 2
            framerate = sample_rate
        
        with wave.open(wav_file, 'wb') as wav:
            wav.setnchannels(channels)
            wav.setsampwidth(sampwidth)
            wav.setframerate(framerate)
            wav.writeframes(pcm_data)
        
        return True
    except Exception as e:
        print(f"✗ PCM转WAV失败: {e}")
        return False

def extract_features_from_audio(wav_file):
    """从WAV音频文件提取RNNoise特征"""
    try:
        print(f"开始提取特征: {wav_file}")
        
        with wave.open(wav_file, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            sample_rate = wav.getframerate()
            channels = wav.getnchannels()
            sampwidth = wav.getsampwidth()
        
        print(f"  音频信息: {sample_rate}Hz, {channels}声道, {sampwidth*8}位")
        
        if len(frames) % 2 != 0:
            frames = frames[:-1]
        
        samples = struct.unpack(f'<{len(frames)//2}h', frames)
        audio_data = np.array(samples, dtype=np.float32) / 32768.0
        
        frame_size = 480
        num_frames = len(audio_data) // frame_size
        
        if num_frames == 0:
            print("✗ 音频太短，无法提取特征")
            return None
        
        print(f"  音频长度: {len(audio_data)} 样本, {num_frames} 帧")
        
        # 提取98维特征
        features = []
        for i in range(num_frames):
            start_idx = i * frame_size
            end_idx = start_idx + frame_size
            
            if end_idx > len(audio_data):
                frame = np.zeros(frame_size, dtype=np.float32)
                frame[:len(audio_data) - start_idx] = audio_data[start_idx:]
            else:
                frame = audio_data[start_idx:end_idx]
            
            frame_features = extract_98d_features(frame)
            features.append(frame_features)
        
        features_array = np.array(features, dtype=np.float32)
        
        # 保存到临时文件
        temp_features = tempfile.NamedTemporaryFile(suffix='.f32', delete=False)
        temp_features_path = temp_features.name
        temp_features.close()
        
        features_array.flatten().tofile(temp_features_path)
        
        print(f"✓ 特征提取成功: {features_array.shape} -> {temp_features_path}")
        return temp_features_path
        
    except Exception as e:
        print(f"✗ 特征提取失败: {e}")
        return None

def extract_98d_features(frame):
    """提取单帧的98维特征"""
    try:
        # FFT变换
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame[:256])
        fft_power = fft_magnitude ** 2
        
        # 输入特征 (65维)
        input_features = np.zeros(65, dtype=np.float32)
        
        # 频谱能量特征 (32维) - 对应RNNoise的32个频带
        for i in range(32):
            start_bin = int(i * 256 / 32)
            end_bin = int((i + 1) * 256 / 32)
            input_features[i] = np.log10(np.mean(fft_power[start_bin:end_bin]) + 1e-10)
        
        # 功率谱特征 (16维)
        for i in range(16):
            start_bin = int(i * 256 / 16)
            end_bin = int((i + 1) * 256 / 16)
            input_features[32 + i] = np.log10(np.mean(fft_power[start_bin:end_bin]) + 1e-10)
        
        # 时域特征 (17维)
        input_features[48] = np.log10(np.mean(np.abs(frame)) + 1e-10)
        input_features[49] = np.log10(np.std(frame) + 1e-10)
        input_features[50] = np.log10(np.max(np.abs(frame)) + 1e-10)
        input_features[51] = np.log10(np.sum(frame ** 2) + 1e-10)
        
        # 零交叉率
        zero_crossings = np.sum(np.diff(np.sign(frame)) != 0)
        input_features[52] = zero_crossings / len(frame)
        
        # 频谱质心
        freqs = np.arange(256) * 48000 / 512
        spectral_centroid = np.sum(freqs * fft_magnitude) / (np.sum(fft_magnitude) + 1e-10)
        input_features[53] = spectral_centroid / 24000
        
        # 频谱带宽
        spectral_bandwidth = np.sqrt(np.sum(((freqs - spectral_centroid) ** 2) * fft_magnitude) / (np.sum(fft_magnitude) + 1e-10))
        input_features[54] = spectral_bandwidth / 24000
        
        # 频谱滚降
        cumsum_magnitude = np.cumsum(fft_magnitude)
        rolloff_point = np.where(cumsum_magnitude >= 0.85 * cumsum_magnitude[-1])[0]
        if len(rolloff_point) > 0:
            input_features[55] = rolloff_point[0] / 256
        else:
            input_features[55] = 1.0
        
        # 其他特征填充
        for i in range(56, 65):
            input_features[i] = np.random.normal(0, 0.01)
        
        # 目标增益 (32维) - 初始化为适中的值
        target_gain = np.ones(32, dtype=np.float32) * 0.5
        
        # 目标VAD (1维) - 基于能量判断
        energy = np.sum(frame ** 2)
        target_vad = np.array([1.0 if energy > 1e-6 else 0.0], dtype=np.float32)
        
        # 合并为98维特征
        features_98d = np.concatenate([input_features, target_gain, target_vad])
        
        return features_98d
        
    except Exception as e:
        print(f"单帧特征提取失败: {e}")
        return np.zeros(98, dtype=np.float32)

def load_model(model_path, device):
    """加载训练好的RNNoise模型"""
    print(f"加载模型: {model_path}")
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    checkpoint = torch.load(model_path, map_location='cpu')
    
    model_args = checkpoint.get('model_args', ())
    model_kwargs = checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 256})
    
    print(f"模型参数: args={model_args}, kwargs={model_kwargs}")
    
    model = rnnoise.RNNoise(*model_args, **model_kwargs)
    model.load_state_dict(checkpoint['state_dict'])
    model.to(device)
    model.eval()
    
    print(f"✓ 模型加载成功")
    print(f"  参数数量: {sum(p.numel() for p in model.parameters())}")
    print(f"  训练轮次: {checkpoint.get('epoch', 'Unknown')}")
    print(f"  训练损失: {checkpoint.get('loss', 'Unknown')}")
    
    return model

class RNNoiseTestDataset(torch.utils.data.Dataset):
    """RNNoise测试数据集类"""
    def __init__(self, features_file, sequence_length=2000):
        self.sequence_length = sequence_length

        self.data = np.memmap(features_file, dtype='float32', mode='r')
        dim = 98

        self.nb_sequences = self.data.shape[0] // self.sequence_length // dim
        if self.nb_sequences == 0:
            actual_length = self.data.shape[0] // dim
            if actual_length > 0:
                self.sequence_length = actual_length
                self.nb_sequences = 1
                self.data = self.data[:actual_length * dim]
                self.data = np.reshape(self.data, (1, actual_length, dim))
            else:
                raise ValueError("特征数据不足")
        else:
            self.data = self.data[:self.nb_sequences * self.sequence_length * dim]
            self.data = np.reshape(self.data, (self.nb_sequences, self.sequence_length, dim))

        print(f"测试数据集:")
        print(f"  序列数量: {self.nb_sequences}")
        print(f"  序列长度: {self.sequence_length}")
        print(f"  特征维度: {dim}")

    def __len__(self):
        return self.nb_sequences

    def __getitem__(self, index):
        return (self.data[index, :, :65].copy(),      # 输入特征 (65维)
                self.data[index, :, 65:-1].copy(),    # 目标增益 (32维)
                self.data[index, :, -1:].copy())      # 目标VAD (1维)

def denoise_with_rnnoise_improved(model, features_file, pcm_file, output_pcm_file, device,
                                 noise_reduction_factor=2.0, vad_threshold=0.3):
    """改进的RNNoise降噪处理"""
    try:
        print("开始改进版RNNoise降噪处理...")
        print(f"降噪强度: {noise_reduction_factor}")
        print(f"VAD阈值: {vad_threshold}")

        # 1. 加载测试数据集
        test_dataset = RNNoiseTestDataset(features_file)

        # 2. 读取原始音频数据
        with open(pcm_file, 'rb') as f:
            data = f.read()

        if len(data) % 2 != 0:
            data = data[:-1]

        samples = struct.unpack(f'<{len(data)//2}h', data)
        audio_data = np.array(samples, dtype=np.float32) / 32768.0

        print(f"原始音频长度: {len(audio_data)} 样本")

        # 3. 使用模型进行推理
        denoised_audio = np.copy(audio_data)
        frame_size = 480

        # 统计信息
        total_frames = 0
        speech_frames = 0
        noise_frames = 0

        with torch.no_grad():
            states = None

            for seq_idx in range(len(test_dataset)):
                features, _, _ = test_dataset[seq_idx]

                features_tensor = torch.from_numpy(features).unsqueeze(0).to(device)

                # RNNoise前向传播
                pred_gain, pred_vad, states = model(features_tensor, states)

                pred_gain = pred_gain.squeeze(0).cpu().numpy()
                pred_vad = pred_vad.squeeze(0).cpu().numpy()

                # 应用降噪到对应的音频段
                start_audio_idx = seq_idx * test_dataset.sequence_length * frame_size

                for frame_idx in range(len(pred_gain)):
                    audio_start = start_audio_idx + frame_idx * frame_size
                    audio_end = min(audio_start + frame_size, len(audio_data))

                    if audio_start < len(audio_data) and audio_end <= len(audio_data):
                        frame = audio_data[audio_start:audio_end]
                        gain = pred_gain[frame_idx]
                        vad_score = pred_vad[frame_idx][0]

                        # 统计
                        total_frames += 1
                        if vad_score > vad_threshold:
                            speech_frames += 1
                        else:
                            noise_frames += 1

                        # 应用改进的频域降噪
                        denoised_frame = apply_improved_spectral_denoising(
                            frame, gain, vad_score, noise_reduction_factor, vad_threshold)

                        if len(denoised_frame) == len(frame):
                            denoised_audio[audio_start:audio_end] = denoised_frame

        # 4. 统计信息
        print(f"处理统计:")
        print(f"  总帧数: {total_frames}")
        print(f"  语音帧: {speech_frames} ({speech_frames/total_frames*100:.1f}%)")
        print(f"  噪声帧: {noise_frames} ({noise_frames/total_frames*100:.1f}%)")

        # 5. 保存降噪后的音频
        denoised_samples = np.clip(denoised_audio * 32768.0, -32768, 32767).astype(np.int16)

        with open(output_pcm_file, 'wb') as f:
            f.write(denoised_samples.tobytes())

        print(f"✓ 降噪处理完成: {output_pcm_file}")
        print(f"✓ 输出长度: {len(denoised_samples)} 样本")
        return True

    except Exception as e:
        print(f"✗ 降噪处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def apply_improved_spectral_denoising(frame, gain, vad_score, noise_reduction_factor=2.0, vad_threshold=0.3):
    """改进的频谱降噪算法"""
    try:
        if len(frame) == 0:
            return frame

        frame_size = 480
        original_length = len(frame)

        if len(frame) < frame_size:
            padded_frame = np.zeros(frame_size, dtype=np.float32)
            padded_frame[:len(frame)] = frame
            frame = padded_frame
        elif len(frame) > frame_size:
            frame = frame[:frame_size]

        # FFT变换
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame)
        fft_phase = np.angle(fft_frame)

        # 应用32频带增益
        denoised_magnitude = np.copy(fft_magnitude)

        # 判断是否为语音帧
        is_speech = vad_score > vad_threshold

        for band in range(32):
            start_bin = int(band * 256 / 32)
            end_bin = int((band + 1) * 256 / 32)

            band_gain = gain[band]

            # 改进的增益处理
            if is_speech:
                # 语音帧：保护语音，适度降噪
                processed_gain = np.clip(band_gain, 0.3, 1.2)
                # 应用sigmoid函数平滑增益
                processed_gain = 1.0 / (1.0 + np.exp(-5 * (processed_gain - 0.5)))
                applied_gain = 0.7 + 0.3 * processed_gain
            else:
                # 噪声帧：强力降噪
                processed_gain = np.clip(band_gain, 0.0, 0.8)
                # 应用更强的降噪
                processed_gain = processed_gain ** noise_reduction_factor
                applied_gain = 0.1 + 0.4 * processed_gain

            # 频率相关的调整
            if band < 8:  # 低频 (0-3kHz)
                applied_gain *= 0.9  # 稍微减弱低频噪声
            elif band > 24:  # 高频 (12kHz+)
                applied_gain *= 0.7  # 更多减弱高频噪声

            # 应用增益
            denoised_magnitude[start_bin:end_bin] *= applied_gain
            # 处理对称部分
            if start_bin > 0:
                denoised_magnitude[512-end_bin:512-start_bin] *= applied_gain

        # 重构复数频谱
        denoised_fft = denoised_magnitude * np.exp(1j * fft_phase)

        # IFFT回时域
        denoised_frame = np.real(np.fft.ifft(denoised_fft))[:frame_size]

        # 限制幅度
        denoised_frame = np.clip(denoised_frame, -1.0, 1.0)

        return denoised_frame[:original_length]

    except Exception as e:
        print(f"频谱降噪失败: {e}")
        return frame

def test_single_audio_improved(model_path, input_wav, output_wav, temp_dir="temp_denoise_improved",
                              noise_reduction_factor=3.0, vad_threshold=0.5):
    """改进版单个音频文件降噪测试"""
    print("=== 改进版RNNoise音频降噪验证 ===")
    print(f"模型路径: {model_path}")
    print(f"输入音频: {input_wav}")
    print(f"输出音频: {output_wav}")
    print(f"降噪强度: {noise_reduction_factor}")
    print(f"VAD阈值: {vad_threshold}")

    os.makedirs(temp_dir, exist_ok=True)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    try:
        # 验证文件
        if not os.path.exists(input_wav):
            print(f"✗ 输入音频文件不存在: {input_wav}")
            return False

        if not os.path.exists(model_path):
            print(f"✗ 模型文件不存在: {model_path}")
            return False

        # 转换音频为48kHz
        temp_48k_wav = os.path.join(temp_dir, "temp_48k.wav")
        if not convert_audio_to_48k(input_wav, temp_48k_wav):
            print("✗ 音频格式转换失败")
            return False

        # 加载模型
        model = load_model(model_path, device)

        # WAV转PCM
        temp_pcm = os.path.join(temp_dir, "temp_input.pcm")
        if not wav_to_pcm(temp_48k_wav, temp_pcm):
            print("✗ WAV转PCM失败")
            return False

        # 提取特征
        features_file = extract_features_from_audio(temp_48k_wav)
        if features_file is None:
            print("✗ 特征提取失败")
            return False

        # 改进版RNNoise降噪
        denoised_pcm = os.path.join(temp_dir, "temp_denoised.pcm")
        if not denoise_with_rnnoise_improved(model, features_file, temp_pcm, denoised_pcm,
                                           device, noise_reduction_factor, vad_threshold):
            print("✗ 降噪处理失败")
            return False

        # PCM转WAV
        if not pcm_to_wav(denoised_pcm, output_wav, reference_wav=temp_48k_wav):
            print("✗ 降噪音频PCM转WAV失败")
            return False

        # 验证输出文件
        try:
            with wave.open(temp_48k_wav, 'rb') as orig:
                orig_frames = orig.getnframes()
                orig_rate = orig.getframerate()

            with wave.open(output_wav, 'rb') as denoised:
                denoised_frames = denoised.getnframes()
                denoised_rate = denoised.getframerate()

            print(f"文件验证:")
            print(f"  原始: {orig_frames}帧, {orig_rate}Hz")
            print(f"  降噪: {denoised_frames}帧, {denoised_rate}Hz")
            print(f"  原始时长: {orig_frames / orig_rate:.2f}秒")
            print(f"  降噪时长: {denoised_frames / denoised_rate:.2f}秒")

        except Exception as e:
            print(f"⚠️ 文件验证失败: {e}")

        # 清理临时特征文件
        try:
            if os.path.exists(features_file):
                os.unlink(features_file)
        except:
            pass

        print(f"✓ 改进版降噪处理完成!")
        print(f"✓ 输出文件: {output_wav}")
        print(f"✓ 临时文件: {temp_dir}")
        print(f"\n🎧 建议:")
        print(f"  1. 播放原始音频: {input_wav}")
        print(f"  2. 播放降噪音频: {output_wav}")
        print(f"  3. 对比降噪效果")
        print(f"  4. 如果效果不明显，可以调整降噪强度参数")

        return True

    except Exception as e:
        print(f"✗ 处理过程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='改进版RNNoise音频降噪验证脚本')
    parser.add_argument('--model', '-m', required=True,
                       help='RNNoise模型文件路径')
    parser.add_argument('--input', '-i', required=True,
                       help='输入音频文件路径')
    parser.add_argument('--output', '-o', required=True,
                       help='输出降噪音频文件路径')
    parser.add_argument('--temp-dir', '-t', default='temp_denoise_improved',
                       help='临时文件目录')
    parser.add_argument('--noise-reduction', '-n', type=float, default=2.0,
                       help='降噪强度 (1.0-5.0, 默认2.0)')
    parser.add_argument('--vad-threshold', '-v', type=float, default=0.3,
                       help='VAD阈值 (0.0-1.0, 默认0.3)')

    args = parser.parse_args()

    print("🎯 改进版RNNoise音频降噪验证脚本")
    print("=" * 50)

    # 转换为绝对路径
    model_path = os.path.abspath(args.model)
    input_wav = os.path.abspath(args.input)
    output_wav = os.path.abspath(args.output)
    temp_dir = os.path.abspath(args.temp_dir)

    # 参数验证
    if args.noise_reduction < 1.0 or args.noise_reduction > 5.0:
        print("⚠️ 降噪强度应在1.0-5.0之间，使用默认值2.0")
        args.noise_reduction = 2.0

    if args.vad_threshold < 0.0 or args.vad_threshold > 1.0:
        print("⚠️ VAD阈值应在0.0-1.0之间，使用默认值0.3")
        args.vad_threshold = 0.3

    # 开始处理
    try:
        success = test_single_audio_improved(
            model_path, input_wav, output_wav, temp_dir,
            args.noise_reduction, args.vad_threshold)

        if success:
            print("\n🎉 改进版降噪验证成功完成！")
            print(f"📁 输出文件: {output_wav}")
            print(f"📁 临时文件: {temp_dir}")
            print(f"\n💡 调优建议:")
            print(f"  - 如果降噪不够：增加 --noise-reduction 参数 (如 3.0)")
            print(f"  - 如果语音失真：降低 --noise-reduction 参数 (如 1.5)")
            print(f"  - 如果误判语音：调整 --vad-threshold 参数 (如 0.2 或 0.5)")
        else:
            print("\n❌ 改进版降噪验证失败")
            return 1

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 验证过程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
