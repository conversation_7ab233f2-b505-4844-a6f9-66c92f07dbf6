#!/usr/bin/env python3
"""
改进版RNNoise降噪测试运行脚本
使用增强的降噪算法和可调参数

使用方法:
python run_improved_denoise_test.py
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    print("🎯 改进版RNNoise降噪测试运行脚本")
    print("=" * 50)
    
    # 获取当前脚本所在目录
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent
    
    # 预设路径
    model_path = current_dir / "output" / "checkpoints" / "rnnoise_69.pth"
    input_wav = current_dir / "20250716_084323.wav"
    output_wav = current_dir / "denoised_improved_20250716_084323.wav"
    temp_dir = current_dir / "temp_denoise_improved"
    
    print(f"项目根目录: {project_root}")
    print(f"当前目录: {current_dir}")
    print(f"模型文件: {model_path}")
    print(f"输入音频: {input_wav}")
    print(f"输出音频: {output_wav}")
    print(f"临时目录: {temp_dir}")
    
    # 检查文件是否存在
    if not model_path.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        print("请确认模型文件路径是否正确")
        return 1
    
    if not input_wav.exists():
        print(f"❌ 输入音频文件不存在: {input_wav}")
        print("请确认音频文件路径是否正确")
        return 1
    
    # 检查Python环境
    try:
        import torch
        import numpy as np
        print(f"✓ PyTorch版本: {torch.__version__}")
        print(f"✓ NumPy版本: {np.__version__}")
        print(f"✓ CUDA可用: {torch.cuda.is_available()}")
    except ImportError as e:
        print(f"❌ Python依赖缺失: {e}")
        return 1
    
    # 检查rnnoise模块
    try:
        sys.path.append(str(current_dir))
        import rnnoise
        print("✓ RNNoise模块加载成功")
    except ImportError as e:
        print(f"❌ RNNoise模块加载失败: {e}")
        print("请确认rnnoise.py文件存在且可导入")
        return 1
    
    # 构建命令
    test_script = current_dir / "test_audio_denoising_improved.py"
    if not test_script.exists():
        print(f"❌ 改进版测试脚本不存在: {test_script}")
        return 1
    
    # 提供不同的降噪强度选项
    print("\n🔧 降噪参数选择:")
    print("1. 轻度降噪 (noise-reduction=1.5, vad-threshold=0.4)")
    print("2. 中度降噪 (noise-reduction=2.0, vad-threshold=0.3) [默认]")
    print("3. 强度降噪 (noise-reduction=3.0, vad-threshold=0.2)")
    print("4. 自定义参数")
    
    try:
        choice = input("请选择降噪强度 (1-4, 默认2): ").strip()
        if not choice:
            choice = "2"
        
        if choice == "1":
            noise_reduction = 1.5
            vad_threshold = 0.4
        elif choice == "3":
            noise_reduction = 3.0
            vad_threshold = 0.2
        elif choice == "4":
            try:
                noise_reduction = float(input("请输入降噪强度 (1.0-5.0, 默认2.0): ") or "2.0")
                vad_threshold = float(input("请输入VAD阈值 (0.0-1.0, 默认0.3): ") or "0.3")
                
                # 参数验证
                noise_reduction = max(1.0, min(5.0, noise_reduction))
                vad_threshold = max(0.0, min(1.0, vad_threshold))
            except ValueError:
                print("⚠️ 参数输入错误，使用默认值")
                noise_reduction = 2.0
                vad_threshold = 0.3
        else:  # 默认选择2
            noise_reduction = 2.0
            vad_threshold = 0.3
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户取消操作")
        return 1
    
    cmd = [
        sys.executable,  # Python解释器
        str(test_script),
        "--model", str(model_path),
        "--input", str(input_wav),
        "--output", str(output_wav),
        "--temp-dir", str(temp_dir),
        "--noise-reduction", str(noise_reduction),
        "--vad-threshold", str(vad_threshold)
    ]
    
    print(f"\n执行命令:")
    print(f"  降噪强度: {noise_reduction}")
    print(f"  VAD阈值: {vad_threshold}")
    print(f"  {' '.join(cmd)}")
    print("\n" + "=" * 50)
    
    # 执行测试脚本
    try:
        result = subprocess.run(cmd, cwd=str(current_dir))
        
        if result.returncode == 0:
            print(f"\n🎉 改进版降噪测试完成！")
            print(f"📁 输出文件: {output_wav}")
            
            # 提供播放建议
            if output_wav.exists():
                print(f"\n🎧 播放建议:")
                print(f"  原始音频: {input_wav}")
                print(f"  降噪音频: {output_wav}")
                print(f"\n💡 如果效果不理想，可以:")
                print(f"  - 重新运行脚本并选择不同的降噪强度")
                print(f"  - 调整VAD阈值来改善语音检测")
        
        return result.returncode
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
