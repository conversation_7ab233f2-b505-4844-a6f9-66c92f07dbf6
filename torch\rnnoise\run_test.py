#!/usr/bin/env python3
"""
运行RNNoise测试脚本
"""

import os
import sys

def main():
    print("🎯 运行RNNoise测试")
    print("=" * 40)
    
    # 检查必要文件
    model_path = "../../RNNoise_train/model_output/checkpoints/rnnoise_test10_5.pth"
    input_wav = "../../RNNoise_train/test_voice/20250716_084323.wav"
    
    print(f"检查模型文件: {model_path}")
    if os.path.exists(model_path):
        print("✓ 模型文件存在")
    else:
        print("✗ 模型文件不存在")
        return
    
    print(f"检查输入音频: {input_wav}")
    if os.path.exists(input_wav):
        print("✓ 输入音频存在")
    else:
        print("✗ 输入音频不存在")
        return
    
    # 运行test.py
    print("\n开始运行test.py...")
    try:
        import test
        print("✓ test.py运行完成")
    except Exception as e:
        print(f"✗ test.py运行失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 检查输出文件
    output_wav = "denoised.wav"
    if os.path.exists(output_wav):
        size = os.path.getsize(output_wav)
        print(f"✓ 输出文件生成: {output_wav} ({size/1024:.1f}KB)")
    else:
        print("✗ 输出文件未生成")

if __name__ == "__main__":
    main()
