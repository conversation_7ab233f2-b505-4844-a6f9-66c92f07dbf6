#!/usr/bin/env python3
"""
修复特征提取的RNNoise测试脚本
基于诊断结果修复特征数值范围问题
"""

import numpy as np
import torch
import os
import sys
import struct
import wave
import subprocess
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import rnnoise

def convert_to_mono_48k(input_wav, output_wav):
    """转换音频为单声道48kHz"""
    try:
        ffmpeg_path = Path(r"D:\RNNoise\rnnoise-plus-main\env\ffmpeg.exe")
        
        if not ffmpeg_path.exists():
            print(f"❌ FFmpeg不存在: {ffmpeg_path}")
            return False
        
        cmd = [
            str(ffmpeg_path), '-y',
            '-i', input_wav,
            '-ar', '48000',
            '-ac', '1',
            '-acodec', 'pcm_s16le',
            output_wav
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        return result.returncode == 0
            
    except Exception as e:
        print(f"❌ 音频转换失败: {e}")
        return False

def extract_fixed_features(frame):
    """修复的特征提取 - 匹配训练时的数值范围"""
    try:
        # 确保帧长度为480
        if len(frame) != 480:
            padded_frame = np.zeros(480, dtype=np.float32)
            padded_frame[:min(len(frame), 480)] = frame[:min(len(frame), 480)]
            frame = padded_frame
        
        # 应用汉宁窗
        windowed_frame = frame * np.hanning(480)
        
        # FFT变换
        fft_frame = np.fft.fft(windowed_frame, n=512)
        fft_magnitude = np.abs(fft_frame[:256])
        fft_power = fft_magnitude ** 2
        
        # 初始化65维特征
        features = np.zeros(65, dtype=np.float32)
        
        # 使用Bark频率尺度的32个频带
        bark_bands = np.array([0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 14, 16, 20, 24, 28, 32, 38, 46, 54, 64, 76, 90, 106, 124, 144, 168, 196, 230, 270, 318, 378, 256])
        
        # 前32维：Bark频带的对数功率谱
        total_power = np.sum(fft_power) + 1e-10
        for i in range(32):
            start_bin = bark_bands[i]
            end_bin = min(bark_bands[i + 1], 256)
            if end_bin > start_bin:
                band_power = np.sum(fft_power[start_bin:end_bin])
                # 归一化并取对数，限制范围
                normalized_power = band_power / total_power
                features[i] = np.log(max(normalized_power, 1e-10))
                # 限制范围到[-10, 5]
                features[i] = np.clip(features[i], -10, 5)
        
        # 第33-48维：16个频带的归一化线性功率谱
        for i in range(16):
            start_bin = int(i * 256 / 16)
            end_bin = int((i + 1) * 256 / 16)
            if end_bin > start_bin:
                band_power = np.sum(fft_power[start_bin:end_bin])
                # 归一化到[0, 1]范围
                features[32 + i] = band_power / total_power
                # 进一步限制范围
                features[32 + i] = np.clip(features[32 + i], 0, 1)
        
        # 第49-52维：时域特征（对数域，限制范围）
        rms = np.sqrt(np.mean(frame ** 2))
        features[48] = np.clip(np.log(max(rms, 1e-10)), -10, 5)
        
        std_val = np.std(frame)
        features[49] = np.clip(np.log(max(std_val, 1e-10)), -10, 5)
        
        peak_val = np.max(np.abs(frame))
        features[50] = np.clip(np.log(max(peak_val, 1e-10)), -10, 5)
        
        energy = np.sum(frame ** 2)
        features[51] = np.clip(np.log(max(energy, 1e-10)), -10, 5)
        
        # 第53维：零交叉率（归一化到[0, 1]）
        zero_crossings = np.sum(np.diff(np.sign(frame)) != 0)
        features[52] = zero_crossings / len(frame)
        
        # 第54-55维：频谱特征（归一化到[0, 1]）
        if np.sum(fft_magnitude) > 1e-10:
            freqs = np.arange(256) * 48000 / 512
            spectral_centroid = np.sum(freqs * fft_magnitude) / np.sum(fft_magnitude)
            features[53] = spectral_centroid / 24000  # 归一化到[0, 1]
            
            spectral_bandwidth = np.sqrt(np.sum(((freqs - spectral_centroid) ** 2) * fft_magnitude) / np.sum(fft_magnitude))
            features[54] = spectral_bandwidth / 24000  # 归一化到[0, 1]
        else:
            features[53] = 0.5
            features[54] = 0.5
        
        # 第56-65维：设为小的随机值或0
        for i in range(55, 65):
            features[i] = 0.0
        
        return features
        
    except Exception as e:
        print(f"特征提取失败: {e}")
        return np.zeros(65, dtype=np.float32)

def apply_fixed_spectral_denoising(frame, gain, vad_score):
    """修复的频谱降噪 - 基于模型实际输出范围调整"""
    try:
        if len(frame) == 0:
            return frame
        
        # 确保帧长度
        original_length = len(frame)
        if len(frame) != 480:
            padded_frame = np.zeros(480, dtype=np.float32)
            padded_frame[:min(len(frame), 480)] = frame[:min(len(frame), 480)]
            frame = padded_frame
        
        # FFT变换
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame)
        fft_phase = np.angle(fft_frame)
        
        # 应用32频带增益
        denoised_magnitude = np.copy(fft_magnitude)
        
        # 基于诊断结果调整VAD阈值和增益处理
        # 模型的VAD输出范围大约是[0.0, 1.0]，均值约0.5-0.7
        is_speech = vad_score > 0.6  # 稍微提高阈值
        
        for band in range(32):
            start_bin = int(band * 256 / 32)
            end_bin = int((band + 1) * 256 / 32)
            
            if end_bin <= start_bin:
                continue
            
            # 获取该频带的增益
            band_gain = gain[band]
            
            # 基于模型实际输出范围调整增益处理
            # 模型增益输出范围大约是[0.1, 0.95]
            if is_speech:
                # 语音帧：保护语音，映射到[0.8, 1.1]
                processed_gain = 0.8 + 0.3 * band_gain
            else:
                # 噪声帧：降噪，映射到[0.2, 0.8]
                processed_gain = 0.2 + 0.6 * band_gain
            
            # 频率相关的微调
            if band < 8:  # 低频保护
                processed_gain = max(processed_gain, 0.7)
            elif band > 24:  # 高频可以更多降噪
                processed_gain *= 0.8
            
            # 限制增益范围
            processed_gain = np.clip(processed_gain, 0.1, 1.2)
            
            # 应用增益
            denoised_magnitude[start_bin:end_bin] *= processed_gain
            # 处理对称部分
            if start_bin > 0 and 512-end_bin >= 0 and 512-start_bin <= 512:
                denoised_magnitude[512-end_bin:512-start_bin] *= processed_gain
        
        # 重构复数频谱
        denoised_fft = denoised_magnitude * np.exp(1j * fft_phase)
        
        # IFFT回时域
        denoised_frame = np.real(np.fft.ifft(denoised_fft))[:480]
        
        # 温和的幅度限制
        max_val = max(np.max(np.abs(frame)), 0.01)
        denoised_frame = np.clip(denoised_frame, -max_val*1.2, max_val*1.2)
        
        return denoised_frame[:original_length]

    except Exception as e:
        print(f"频谱降噪失败: {e}")
        return frame[:original_length] if len(frame) >= original_length else frame

def rnnoise_denoise_fixed(model_path, input_wav, output_wav):
    """修复版RNNoise降噪"""
    print(f"🔧 修复版RNNoise降噪测试")
    print(f"模型: {model_path}")
    print(f"输入: {input_wav}")
    print(f"输出: {output_wav}")
    
    try:
        # 1. 转换音频格式
        temp_wav = "temp_mono_48k_fixed.wav"
        if not convert_to_mono_48k(input_wav, temp_wav):
            print("❌ 音频转换失败")
            return False
        
        # 2. 加载模型
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"使用设备: {device}")
        
        checkpoint = torch.load(model_path, map_location='cpu')
        model_kwargs = checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 256})
        
        model = rnnoise.RNNoise(**model_kwargs)
        model.load_state_dict(checkpoint['state_dict'])
        model.to(device)
        model.eval()
        
        print(f"✓ 模型加载成功 (训练轮次: {checkpoint.get('epoch', 'Unknown')})")
        
        # 3. 读取音频
        with wave.open(temp_wav, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            sample_rate = wav.getframerate()
        
        if len(frames) % 2 != 0:
            frames = frames[:-1]
        
        samples = struct.unpack(f'<{len(frames)//2}h', frames)
        audio_data = np.array(samples, dtype=np.float32) / 32768.0
        
        print(f"音频信息: {len(audio_data)}样本, {len(audio_data)/sample_rate:.2f}秒")
        
        # 4. 逐帧处理
        frame_size = 480
        num_frames = len(audio_data) // frame_size
        denoised_audio = np.copy(audio_data)
        
        print(f"开始修复版降噪处理 ({num_frames}帧)...")
        
        # 统计模型输出范围
        gain_values = []
        vad_values = []
        
        with torch.no_grad():
            states = None
            
            # 分批处理
            batch_size = 200
            for i in range(0, num_frames, batch_size):
                end_frame = min(i + batch_size, num_frames)
                
                # 提取特征
                features_batch = []
                for j in range(i, end_frame):
                    start_idx = j * frame_size
                    end_idx = start_idx + frame_size
                    
                    if end_idx <= len(audio_data):
                        frame = audio_data[start_idx:end_idx]
                    else:
                        frame = np.zeros(frame_size)
                        remaining = len(audio_data) - start_idx
                        if remaining > 0:
                            frame[:remaining] = audio_data[start_idx:]
                    
                    # 使用修复的特征提取
                    frame_features = extract_fixed_features(frame)
                    features_batch.append(frame_features)
                
                # 转换为tensor并推理
                if features_batch:
                    features_tensor = torch.from_numpy(np.array(features_batch)).unsqueeze(0).to(device)
                    gain, vad, states = model(features_tensor, states)
                    
                    # 收集统计信息
                    gain_values.extend(gain.flatten().cpu().numpy())
                    vad_values.extend(vad.flatten().cpu().numpy())
                    
                    # 应用降噪
                    gain_np = gain.squeeze(0).cpu().numpy()
                    vad_np = vad.squeeze(0).cpu().numpy()
                    
                    for k, (frame_gain, frame_vad) in enumerate(zip(gain_np, vad_np)):
                        frame_idx = i + k
                        if frame_idx < num_frames:
                            start_idx = frame_idx * frame_size
                            end_idx = min(start_idx + frame_size, len(audio_data))
                            
                            if start_idx < len(audio_data):
                                frame = audio_data[start_idx:end_idx]
                                
                                # 应用修复的降噪
                                denoised_frame = apply_fixed_spectral_denoising(
                                    frame, frame_gain, frame_vad[0])
                                
                                denoised_audio[start_idx:end_idx] = denoised_frame
                
                # 显示进度
                if (i // batch_size + 1) % 5 == 0:
                    progress = min(end_frame, num_frames) / num_frames * 100
                    print(f"  进度: {progress:.1f}%")
        
        # 5. 输出统计信息
        if gain_values and vad_values:
            print(f"\n📊 模型输出统计:")
            print(f"  增益范围: [{np.min(gain_values):.4f}, {np.max(gain_values):.4f}]")
            print(f"  增益均值: {np.mean(gain_values):.4f}")
            print(f"  VAD范围: [{np.min(vad_values):.4f}, {np.max(vad_values):.4f}]")
            print(f"  VAD均值: {np.mean(vad_values):.4f}")
        
        # 6. 保存结果
        denoised_samples = np.clip(denoised_audio * 32768.0, -32768, 32767).astype(np.int16)
        
        with wave.open(output_wav, 'wb') as wav:
            wav.setnchannels(1)
            wav.setsampwidth(2)
            wav.setframerate(sample_rate)
            wav.writeframes(denoised_samples.tobytes())
        
        # 7. 分析效果
        print(f"\n📊 处理结果:")
        orig_rms = np.sqrt(np.mean(audio_data ** 2))
        denoised_rms = np.sqrt(np.mean(denoised_audio ** 2))
        rms_change = (denoised_rms - orig_rms) / orig_rms * 100
        
        print(f"  RMS变化: {rms_change:+.1f}%")
        
        # 频谱分析
        orig_fft = np.abs(np.fft.fft(audio_data[:min(len(audio_data), 48000)]))
        denoised_fft = np.abs(np.fft.fft(denoised_audio[:min(len(denoised_audio), 48000)]))
        
        # 高频变化
        high_freq_orig = np.mean(orig_fft[len(orig_fft)//2:])
        high_freq_denoised = np.mean(denoised_fft[len(denoised_fft)//2:])
        high_freq_change = (high_freq_denoised - high_freq_orig) / high_freq_orig * 100
        
        print(f"  高频变化: {high_freq_change:+.1f}%")
        
        if abs(rms_change) > 5 or abs(high_freq_change) > 10:
            print("  ✓ 检测到明显的降噪效果")
        else:
            print("  ⚠️ 降噪效果不明显")
        
        print(f"✓ 修复版降噪完成: {output_wav}")
        
        # 清理临时文件
        try:
            os.remove(temp_wav)
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ 降噪失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 修复版RNNoise降噪测试")
    print("=" * 50)
    
    current_dir = Path(__file__).parent
    
    # 查找模型文件
    model_path = current_dir / "output" / "checkpoints" / "rnnoise_69.pth"
    if not model_path.exists():
        checkpoints_dir = current_dir / "output" / "checkpoints"
        if checkpoints_dir.exists():
            checkpoint_files = list(checkpoints_dir.glob("rnnoise_*.pth"))
            if checkpoint_files:
                checkpoint_files.sort(key=lambda x: int(x.stem.split('_')[1]))
                model_path = checkpoint_files[-1]
                print(f"使用最新模型: {model_path.name}")
    
    # 查找音频文件
    audio_file = current_dir / "20250716_084323.wav"
    
    if not model_path.exists():
        print(f"❌ 模型文件不存在")
        return 1
    
    if not audio_file.exists():
        print(f"❌ 音频文件不存在")
        return 1
    
    # 执行修复版降噪
    output_file = current_dir / "fixed_denoised.wav"
    
    if rnnoise_denoise_fixed(str(model_path), str(audio_file), str(output_file)):
        print(f"\n🎉 修复版降噪完成！")
        print(f"📁 原始音频: {audio_file}")
        print(f"📁 降噪音频: {output_file}")
        print(f"\n🎧 请对比播放两个文件")
        print(f"💡 这次修复了特征提取的数值范围问题，应该有更好的效果")
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
