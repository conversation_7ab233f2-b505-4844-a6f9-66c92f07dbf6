# RNNoise音频降噪验证脚本

本目录包含了完整的RNNoise音频降噪验证代码，基于您的训练架构实现。

## 文件说明

### 核心脚本
- `test_audio_denoising.py` - 主要的降噪验证脚本
- `run_denoise_test.py` - 简化的运行脚本
- `run_denoise_test.bat` - Windows批处理脚本

### 模型和数据
- `output/checkpoints/rnnoise_69.pth` - 训练好的RNNoise模型
- `20250716_084323.wav` - 输入测试音频
- `rnnoise.py` - RNNoise模型定义

## 使用方法

### 方法1: 使用批处理脚本（推荐）

1. 确保所有文件都在正确位置
2. 双击运行 `run_denoise_test.bat`
3. 脚本会自动检查环境和文件，然后执行降噪测试

### 方法2: 使用Python脚本

```bash
# 激活Python环境
call ..\..\env\Scripts\activate.bat

# 运行简化脚本
python run_denoise_test.py

# 或者直接运行主脚本
python test_audio_denoising.py --model output/checkpoints/rnnoise_69.pth --input 20250716_084323.wav --output denoised_output.wav
```

### 方法3: 命令行参数

```bash
python test_audio_denoising.py \
    --model "D:\RNNoise\rnnoise-plus-main\torch\rnnoise\output\checkpoints\rnnoise_69.pth" \
    --input "D:\RNNoise\rnnoise-plus-main\torch\rnnoise\20250716_084323.wav" \
    --output "denoised_output.wav" \
    --temp-dir "temp_denoise"
```

## 处理流程

脚本执行以下步骤：

1. **环境检查** - 验证Python环境、依赖库和必要文件
2. **音频预处理** - 使用FFmpeg将音频转换为48kHz采样率
3. **模型加载** - 加载训练好的RNNoise模型
4. **特征提取** - 从音频中提取98维RNNoise特征
5. **降噪处理** - 使用模型进行前向推理，生成增益和VAD预测
6. **频谱处理** - 在频域应用降噪增益
7. **音频重构** - 将处理后的音频保存为WAV文件
8. **结果验证** - 检查输出文件的完整性

## 输出文件

- `denoised_20250716_084323.wav` - 降噪后的音频文件
- `temp_denoise/` - 临时文件目录，包含中间处理文件

## 技术特点

### 完全基于训练架构
- 使用与训练时相同的数据加载方式
- 保持相同的特征提取和处理流程
- 应用相同的增益计算和VAD处理

### 高质量音频处理
- 支持48kHz采样率音频
- 使用FFT进行频域处理
- 32频带增益控制
- VAD辅助的自适应降噪

### 鲁棒性设计
- 完整的错误处理和异常捕获
- 临时文件自动清理
- 详细的处理日志输出

## 依赖要求

### Python库
- torch >= 1.9.0
- numpy >= 1.19.0
- wave (标准库)
- struct (标准库)

### 外部工具
- FFmpeg (位于 `../../env/` 目录)

## 故障排除

### 常见问题

1. **模型文件不存在**
   - 检查 `output/checkpoints/rnnoise_69.pth` 是否存在
   - 确认模型训练已完成

2. **音频文件不存在**
   - 检查 `20250716_084323.wav` 是否在当前目录
   - 确认文件路径正确

3. **FFmpeg错误**
   - 确认 `../../env/ffmpeg.exe` 存在
   - 检查音频文件格式是否支持

4. **内存不足**
   - 对于长音频文件，可能需要更多内存
   - 考虑分段处理长音频

5. **CUDA错误**
   - 脚本会自动选择CPU/GPU
   - 如果GPU内存不足，会自动使用CPU

### 调试模式

如果遇到问题，可以查看 `temp_denoise/` 目录中的中间文件：
- `temp_48k.wav` - 转换后的48kHz音频
- `temp_input.pcm` - 输入PCM数据
- `temp_denoised.pcm` - 降噪后的PCM数据

## 性能优化

### GPU加速
- 脚本会自动检测并使用GPU（如果可用）
- 对于长音频，GPU处理速度显著更快

### 批处理
- 可以修改脚本处理多个音频文件
- 建议对相似长度的音频进行批处理

## 扩展功能

### 自定义参数
可以修改脚本中的以下参数：
- `gamma` - 感知加权指数（默认0.25）
- `sequence_length` - 序列长度（默认2000）
- `frame_size` - 帧大小（默认480）

### 输出格式
- 当前输出48kHz, 16位, 单声道WAV
- 可以修改 `pcm_to_wav` 函数支持其他格式

## 联系支持

如果遇到问题，请检查：
1. 所有依赖是否正确安装
2. 模型文件是否完整
3. 音频文件格式是否支持
4. 系统内存是否充足
