#!/usr/bin/env python3
"""
检查RNNoise模型权重文件的脚本
验证权重文件是否正确加载和使用
"""

import torch
import numpy as np
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import rnnoise

def check_model_file(model_path):
    """检查模型文件的详细信息"""
    print(f"🔍 检查模型文件: {model_path}")
    print("=" * 60)
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    
    # 检查文件大小
    file_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
    print(f"📁 文件大小: {file_size:.2f} MB")
    
    try:
        # 加载检查点
        print("📥 加载检查点...")
        checkpoint = torch.load(model_path, map_location='cpu')
        
        # 检查检查点结构
        print(f"🔑 检查点键: {list(checkpoint.keys())}")
        
        # 检查模型参数
        if 'model_kwargs' in checkpoint:
            model_kwargs = checkpoint['model_kwargs']
            print(f"🏗️ 模型参数: {model_kwargs}")
        else:
            model_kwargs = {'cond_size': 128, 'gru_size': 256}
            print(f"⚠️ 使用默认模型参数: {model_kwargs}")
        
        # 检查训练信息
        if 'epoch' in checkpoint:
            print(f"🔄 训练轮次: {checkpoint['epoch']}")
        
        if 'loss' in checkpoint:
            print(f"📉 训练损失: {checkpoint['loss']}")
        
        # 检查状态字典
        if 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
            print(f"🧠 状态字典键数量: {len(state_dict)}")
            
            # 显示前几个权重层
            print("🔍 权重层信息:")
            for i, (key, value) in enumerate(list(state_dict.items())[:10]):
                print(f"  {i+1:2d}. {key:30s} : {str(value.shape):15s} | {value.dtype}")
            
            if len(state_dict) > 10:
                print(f"     ... 还有 {len(state_dict) - 10} 个权重层")
        else:
            print("❌ 没有找到状态字典")
            return False
        
        return True, checkpoint, model_kwargs
        
    except Exception as e:
        print(f"❌ 加载模型文件失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_loading(model_path):
    """测试模型加载和权重应用"""
    print(f"\n🧪 测试模型加载和权重应用")
    print("=" * 60)
    
    result = check_model_file(model_path)
    if not result:
        return False
    
    success, checkpoint, model_kwargs = result
    
    try:
        # 创建模型
        print("🏗️ 创建RNNoise模型...")
        model_args = checkpoint.get('model_args', ())
        model = rnnoise.RNNoise(*model_args, **model_kwargs)
        
        print(f"✓ 模型创建成功")
        print(f"  输入维度: {model.input_dim}")
        print(f"  输出维度: {model.output_dim}")
        print(f"  条件大小: {model.cond_size}")
        print(f"  GRU大小: {model.gru_size}")
        
        # 加载权重
        print("📥 加载权重到模型...")
        model.load_state_dict(checkpoint['state_dict'])
        print("✓ 权重加载成功")
        
        # 检查权重是否正确加载
        print("🔍 验证权重加载...")
        model_state = model.state_dict()
        checkpoint_state = checkpoint['state_dict']
        
        # 比较几个关键权重
        key_layers = ['conv1.weight', 'conv2.weight', 'gru1.weight_ih_l0', 'dense_out.weight']
        all_match = True
        
        for layer in key_layers:
            if layer in model_state and layer in checkpoint_state:
                model_weight = model_state[layer]
                checkpoint_weight = checkpoint_state[layer]
                
                if torch.allclose(model_weight, checkpoint_weight, atol=1e-6):
                    print(f"  ✓ {layer}: 权重匹配")
                else:
                    print(f"  ❌ {layer}: 权重不匹配")
                    all_match = False
            else:
                print(f"  ⚠️ {layer}: 层不存在")
        
        if all_match:
            print("✅ 所有检查的权重层都正确加载")
        else:
            print("❌ 部分权重层加载有问题")
        
        # 测试前向传播
        print("🚀 测试前向传播...")
        model.eval()
        
        # 创建测试输入
        batch_size = 1
        seq_len = 100
        input_dim = model.input_dim
        
        test_input = torch.randn(batch_size, seq_len, input_dim)
        
        with torch.no_grad():
            gain, vad, states = model(test_input)
        
        print(f"✓ 前向传播成功")
        print(f"  输入形状: {test_input.shape}")
        print(f"  增益输出形状: {gain.shape}")
        print(f"  VAD输出形状: {vad.shape}")
        print(f"  状态数量: {len(states)}")
        
        # 检查输出范围
        gain_min, gain_max = gain.min().item(), gain.max().item()
        vad_min, vad_max = vad.min().item(), vad.max().item()
        
        print(f"  增益范围: [{gain_min:.4f}, {gain_max:.4f}]")
        print(f"  VAD范围: [{vad_min:.4f}, {vad_max:.4f}]")
        
        # 检查输出是否合理
        if 0 <= gain_min and gain_max <= 2.0:
            print("  ✓ 增益输出范围合理")
        else:
            print("  ⚠️ 增益输出范围异常")
        
        if 0 <= vad_min and vad_max <= 1.0:
            print("  ✓ VAD输出范围合理")
        else:
            print("  ⚠️ VAD输出范围异常")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_training_script():
    """与训练脚本的模型创建方式对比"""
    print(f"\n🔄 与训练脚本对比")
    print("=" * 60)
    
    try:
        # 检查训练脚本中的模型参数
        train_script_path = Path(__file__).parent / "train_rnnoise.py"
        if train_script_path.exists():
            print(f"📄 找到训练脚本: {train_script_path}")
            
            # 读取训练脚本内容
            with open(train_script_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找模型创建相关的参数
            if 'cond_size' in content:
                print("✓ 训练脚本中包含 cond_size 参数")
            if 'gru_size' in content:
                print("✓ 训练脚本中包含 gru_size 参数")
            
        else:
            print("⚠️ 未找到训练脚本")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 RNNoise模型权重检查工具")
    print("=" * 60)
    
    # 检查模型文件路径
    current_dir = Path(__file__).parent
    model_path = current_dir / "output" / "checkpoints" / "rnnoise_69.pth"
    
    print(f"📍 当前目录: {current_dir}")
    print(f"🎯 目标模型: {model_path}")
    
    if not model_path.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        print("请确认模型文件路径是否正确")
        return 1
    
    # 执行检查
    tests = [
        ("模型文件检查", lambda: check_model_file(str(model_path))),
        ("模型加载测试", lambda: test_model_loading(str(model_path))),
        ("训练脚本对比", compare_with_training_script),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}失败: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 检查结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        if isinstance(result, tuple):
            result = result[0] if result else False
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20s} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 检查通过")
    
    if passed == total:
        print("🎉 所有检查通过！您的权重文件正在被正确使用。")
        return 0
    else:
        print("⚠️ 部分检查失败，权重文件可能存在问题。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
